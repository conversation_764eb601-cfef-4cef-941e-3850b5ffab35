@import "tailwindcss";
@config "../tailwind.config.mjs";

@theme {
  --font-agenda: "Agenda", "Helvetica", "sans-serif";
  --font-mont: "Mont", "Helvetica", "sans-serif";

  --color-blue-400: #9dd2ef;
  --color-blue-500: #2bbcf1;
  --color-blue-600: #2b7af1;
  --color-blue-700: #333c8c;

  --color-green-400: #9ec44c;
  --color-green-500: #84a43f;
  --color-green-600: #5a9f51;
  --color-green-700: #56884f;

  --color-pink-400: #e074a6;
  --color-pink-500: #f24394;
  --color-pink-600: #cc2e77;
  --color-pink-700: #bb0e5e;

  --color-red-400: #ef3d43;
  --color-red-500: #f6030c;
  --color-red-600: #cb2228;
  --color-red-700: #a60f14;
  --color-error: #eb5757;

  --color-yellow-400: #f4d649;
  --color-yellow-500: #f4eb49;
  --color-yellow-600: #e9bd10;
  --color-yellow-700: #e9b310;

  --color-text-main: #333138;
  --color-text-secondary: #636363;
  --color-background-main: #fafafa;
  --color-background-navbar: #1f1d25;
  --color-background-other: #e0e0e0;
  --color-border-main: #dadada;
  --color-border-input: #cacaca;
  --color-text-main-lighter: rgba(51, 49, 56, 0.65);

  /* Font sizes - Tailwind v4 format */
  --font-size-display-1: 58px;
  --font-size-display-1-mobile: 40px;
  --font-size-heading-1: 42px;
  --font-size-heading-1-mobile: 28px;
  --font-size-heading-2: 34px;
  --font-size-heading-2-mobile: 24px;
  --font-size-heading-3: 32px;
  --font-size-heading-3-mobile: 28px;
  --font-size-text-1: 24px;
  --font-size-text-2: 20px;
  --font-size-body-1: 18px;
  --font-size-body-2: 16px;
  --font-size-body-3: 14px;
  --font-size-button: 16px;
  --font-size-link: 14px;

  /* Line heights */
  --line-height-tight: 120%;
  --line-height-normal: 150%;

  /* Shadows */
  --shadow-elevation-deep:
    2px 3px 7px rgba(0, 0, 0, 0.07), 7px 11px 13px rgba(0, 0, 0, 0.06),
    17px 25px 18px rgba(0, 0, 0, 0.04), 30px 45px 21px rgba(0, 0, 0, 0.01),
    46px 70px 23px rgba(0, 0, 0, 0);
}

/* Font Face Declarations */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  @font-face {
    font-family: "Agenda";
    src: url("./assets/fonts/agenda-regular.otf") format("opentype");
    font-weight: 400;
    font-display: swap;
  }

  @font-face {
    font-family: "Agenda";
    src: url("./assets/fonts/agenda-bold.otf") format("opentype");
    font-weight: 700;
    font-display: swap;
  }

  @font-face {
    font-family: "Mont";
    src: url("./assets/fonts/mont-semibold.otf") format("opentype");
    font-weight: 600;
    font-display: swap;
  }

  body {
    @apply body-2 md:bg-background-main bg-white;
  }

  h1 {
    @apply heading-1;

    @media (width >= 48rem) {
      @apply heading-1-mobile;
    }
  }

  h2 {
    @apply heading-2;

    @media (width >= 48rem) {
      @apply heading-2-mobile;
    }
  }

  h3 {
    @apply heading-3;

    @media (width >= 48rem) {
      @apply heading-3-mobile;
    }
  }
}

/* Typography Components */
@layer components {
  .bwy-container {
    @apply container mx-auto px-6 md:px-0;
  }

  .link-primary {
    @apply link-text py-1 no-underline;
    color: var(--color-blue-600);
  }

  .tw-hidden {
    @apply hidden;
  }

  .card {
    @apply bg-background-main/25 border-border-main overflow-hidden rounded-lg border backdrop-blur-100;
  }

  .card--hover {
    @apply hover:shadow-elevation-deep transition-colors transition-shadow hover:bg-white;
  }

  .news-card--page {
    @apply bg-background-main/75 md:border-border-main rounded-2xl border-0 md:border-1;
  }

  .full-text {
    @apply text-justify text-base leading-relaxed;

    p,
    h2,
    h3,
    h4,
    h5,
    h6 {
      @apply pb-4;
    }

    h2,
    h3,
    h4,
    h5,
    h6 {
      @apply pt-2 font-bold;
    }

    h2 {
      @apply text-3xl tracking-wide uppercase;
    }

    h3 {
      @apply text-2xl;
    }

    h4 {
      @apply text-xl;
    }

    h5 {
      @apply text-lg;
    }

    h6 {
      @apply text-base;
    }

    ul,
    ol {
      @apply list-inside space-y-2;
    }

    ul {
      @apply list-disc;
    }

    ol {
      @apply list-decimal;
    }

    ul li,
    ol li {
      @apply indent-10;
    }

    blockquote {
      @apply border-text-main my-6 border-l-2 pl-4 italic;
    }

    img {
      @apply mx-auto my-8 w-full rounded-lg;
    }

    figure {
      @apply my-8;
    }

    figcaption {
      @apply border-text-main mt-2 border-l-2 pl-4 text-sm italic;
    }
  }

  .ck-editor {
    ul,
    ol {
      @apply list-inside space-y-2;
    }

    ul {
      @apply list-disc;
    }

    ol {
      @apply list-decimal;
    }

    ul li,
    ol li {
      @apply indent-10;

      .ck-list-bogus-paragraph {
        @apply !inline;
      }
    }

  }
}

.toc-js-container nav {
  a {
    @apply text-text-main-lighter relative mb-3 block pl-6 hover:text-blue-600;

    &::before {
      content: "";
      mask-image: url("../src/svg/chevron_right_filled.svg");
      mask-repeat: no-repeat;
      mask-position: center;

      @apply bg-text-main-lighter absolute top-0 left-0 h-5 w-5 transition-colors duration-200;
    }

    &:hover::before {
      @apply bg-blue-600;
    }
  }

  .toc-active {
    > a {
      @apply font-bold text-blue-600;

      &::before {
        @apply bg-blue-600;
      }
    }
  }
}

@utility display-1 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-display-1);
  line-height: var(--line-height-tight);
  font-weight: 700;
}

@utility display-1-mobile {
  font-family: var(--font-agenda);
  font-size: var(--font-size-display-1-mobile);
  line-height: var(--line-height-tight);
  font-weight: 700;
}

@utility heading-1 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-heading-1);
  line-height: var(--line-height-tight);
  font-weight: 700;
}

@utility heading-1-mobile {
  font-family: var(--font-agenda);
  font-size: var(--font-size-heading-1-mobile);
  line-height: var(--line-height-tight);
  font-weight: 700;
}

@utility heading-2 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-heading-2);
  line-height: var(--line-height-tight);
  font-weight: 400;
}

@utility heading-2-mobile {
  font-family: var(--font-agenda);
  font-size: var(--font-size-heading-2-mobile);
  line-height: var(--line-height-tight);
  font-weight: 400;
}

@utility heading-3 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-heading-3);
  line-height: var(--line-height-tight);
  font-weight: 700;
}

@utility heading-3-mobile {
  font-family: var(--font-agenda);
  font-size: var(--font-size-heading-3-mobile);
  line-height: var(--line-height-tight);
  font-weight: 700;
}

@utility text-1 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-text-1);
  line-height: var(--line-height-normal);
  font-weight: 700;
}

@utility text-2 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-text-2);
  line-height: var(--line-height-normal);
  font-weight: 700;
}
@utility body-1 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-body-1);
  line-height: var(--line-height-normal);
  font-weight: 400;
}

@utility body-2 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-body-2);
  line-height: var(--line-height-normal);
  font-weight: 400;
}

@utility body-3 {
  font-family: var(--font-agenda);
  font-size: var(--font-size-body-3);
  line-height: var(--line-height-normal);
  font-weight: 400;
}

@utility btn-text {
  font-family: var(--font-mont);
  font-size: var(--font-size-button);
  line-height: var(--line-height-normal);
  font-weight: 600;
}

@utility link-text {
  font-family: var(--font-mont);
  font-size: var(--font-size-link);
  line-height: var(--line-height-normal);
  font-weight: 600;
}

@layer utilities {
  .content-empty {
    content: "";
  }
}
