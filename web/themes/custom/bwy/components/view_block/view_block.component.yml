name: View Block
description: A reusable component for view blocks with consistent styling and structure
props:
  attributes:
    type: Drupal\Core\Template\Attribute
    description: HTML attributes for the block container
    required: true
  title_prefix:
    type: array
    description: Additional output populated by modules, displayed before the title
    required: false
  title_suffix:
    type: array
    description: Additional output populated by modules, displayed after the title
    required: false
  label:
    type: string
    description: The configured label of the block
    required: false
  content:
    type: mixed
    description: The content of the block
    required: true
  configuration:
    type: array
    description: Block configuration values
    required: true
  plugin_id:
    type: string
    description: The ID of the block implementation
    required: true
