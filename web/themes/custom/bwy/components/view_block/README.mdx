# View Block Component

A reusable component for displaying view blocks with consistent styling and structure.

## Usage

```twig
{% include 'bwy:view_block' with {
  attributes: attributes,
  title_prefix: title_prefix,
  title_suffix: title_suffix,
  label: label,
  content: content,
  configuration: configuration,
  plugin_id: plugin_id
} %}
```

## Properties

- `attributes` (<PERSON><PERSON>al\Core\Template\Attribute, required): HTML attributes for the block container
- `title_prefix` (array, optional): Additional output populated by modules, displayed before the title
- `title_suffix` (array, optional): Additional output populated by modules, displayed after the title
- `label` (string, optional): The configured label of the block
- `content` (mixed, required): The content of the block
- `configuration` (array, required): Block configuration values
- `plugin_id` (string, required): The ID of the block implementation

## Styling

The component uses the following classes:
- `bwy-container`: Container class for consistent width and padding
- `py-60px`: Vertical padding
- `block`: Base block class
- Dynamic classes based on provider and plugin ID

## Examples

### Basic Usage
```twig
{% include 'bwy:view_block' with {
  attributes: attributes,
  label: 'Related News',
  content: content,
  configuration: configuration,
  plugin_id: plugin_id
} %}
```
