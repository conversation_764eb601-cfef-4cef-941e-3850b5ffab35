{%
  set classes = [
    'bwy-container',
    'py-60px',
    'block',
    'block-' ~ configuration.provider|clean_class,
    'block-' ~ plugin_id|clean_class,
  ]
%}

{# Get block ID from content if available #}
{% if elements.content['#block_content'] is defined %}
  {% set id = elements.content['#block_content'].id() %}
  {% if id or attributes.id %}
    {% set block_id = attributes.id ? attributes.id : 'block-id-' ~ id %}
    {% set attributes = attributes.setAttribute('id', block_id) %}
  {% endif %}
{% endif %}

<div{{ attributes.addClass(classes) }}>
  {{ title_prefix }}
  {% if label %}
    {% include 'bwy:heading' with {
      heading_html_tag: 'h2',
      content: label,
    } %}
  {% endif %}
  {{ title_suffix }}
  {% block content %}
    {{ content }}
  {% endblock %}
</div>
