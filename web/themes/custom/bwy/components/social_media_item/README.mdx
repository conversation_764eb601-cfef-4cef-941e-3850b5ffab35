# Social Media Item

A component that renders a single social media link with icon. The text can be optionally displayed alongside the icon using the icon_with_text component's text functionality.

## Props

| Name      | Type    | Required | Default | Description                                      |
|-----------|---------|----------|---------|--------------------------------------------------|
| icon_name | string  | Yes      | -       | Name of the social media icon (e.g. 'facebook') |
| href      | string  | Yes      | -       | URL of the social media profile                 |
| title     | string  | Yes      | -       | Accessible title for screen readers             |
| classes   | string  | No       | -       | CSS classes for styling                         |
| show_text | boolean | No       | false   | Whether to display the text next to the icon    |

## Usage

### Icon Only (Default)
```twig
{% include '@bwy/components/social_media_item/social_media_item.twig' with {
  icon_name: social_media,
  href: link|render,
  title: title,
  classes: 'text-gray-700'
} only %}
```

### With Text
```twig
{% include '@bwy/components/social_media_item/social_media_item.twig' with {
  icon_name: social_media,
  href: link|render,
  title: title,
  classes: 'text-gray-700',
  show_text: true
} only %}
```

## Accessibility Features

The component follows WCAG 2.1 AA guidelines:

- Uses `aria-label` for screen reader description
- Uses semantic HTML (`<a>` element)
- Includes proper link text for screen readers
- Provides screen reader text when visible text is shown

## Security

- Uses `rel="noopener noreferrer"` for external links
- Opens links in new tab with `target="_blank"`

## Dependencies

- Requires the `icon_with_text` component for icon and text rendering. The text display is handled through this component's text property.
