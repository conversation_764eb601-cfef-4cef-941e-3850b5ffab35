# Table of Contents Component

A simple, accessible table of contents component that displays a title with a styled underline.

## Usage

```twig
{% include '@bwy/components/toc/toc.twig' with {
  title: 'Contents',
  tag: 'h2',
  title_attributes: create_attribute().addClass(['custom-class']).setAttribute('id', 'toc-title'),
} %}
```

## Props

| Name | Type | Default | Required | Description |
|------|------|---------|----------|-------------|
| `title` | string | - | No | The title of the table of contents |
| `tag` | string | 'h2' | No | HTML tag to use for the title |
| `title_attributes` | object | - | No | Attributes for the title element (e.g., id, class) |

## Styling

The component uses Tailwind CSS classes for styling:

- Title has a bottom underline using a pseudo-element
- Uses theme colors for text and borders
- Responsive padding and spacing
- Hover and active states for interactive elements

## Accessibility

- Uses semantic HTML with proper heading structure
- ARIA attributes for better screen reader support
- Proper color contrast ratios
- Keyboard navigation support

## Example

```twig
{% include '@bwy/components/toc/toc.twig' with {
  title: 'Page Contents',
  tag: 'h2',
  title_attributes: create_attribute()
    .addClass(['custom-title'])
    .setAttribute('id', 'page-contents')
} %}
```
