{#
/**
 * @file
 * Template for a Table of Content component.
 *
 * Available variables:
 * - attributes: HTML attributes for the component wrapper
 * - title: The title of the table of contents
 * - tag: The HTML tag to use for the title
 * - title_attributes: Attributes for the title element
 * - items: Array of navigation items
 */
#}
{%
  set toc_classes = [
    'p-3',
    'pt-4',
    'border',
    'border-border-main',
    'rounded-lg',
    'bg-white'
  ]
%}

{%
  set title_classes = [
    'body-2',
    'mb-6',
    'inline-block',
    'text-text-main',
    'font-medium',
    'after:content-empty',
    'after:block',
    'after:w-36px',
    'after:h-px',
    'after:bg-border-main',
    'after:mt-2'
  ]
%}

<div{{ attributes.addClass(toc_classes) }}>
  <nav role="navigation" aria-label="{{ 'Table of contents'|t }}"{% if title is not empty %} aria-labelledby="{{ title_attributes.id }}"{% endif %}>
    {% if title is not empty %}
      <{{ tag|default('h2') }} {{ title_attributes.addClass(title_classes) }}>{{ title }}</{{ tag|default('h2') }}>
    {% endif %}
  </nav>
</div>
