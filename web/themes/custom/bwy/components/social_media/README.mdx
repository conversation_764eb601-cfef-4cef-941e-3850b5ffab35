# Social Media

A component that renders a collection of social media links.

## Props

| Name    | Type   | Required | Description                                    |
|---------|--------|----------|------------------------------------------------|
| items   | array  | Yes      | Array of social media items                    |
| classes | string | No       | Additional CSS classes for the wrapper element |

## Usage

```twig
{% set classes = [
  'flex',
  'flex-wrap',
  'gap-2',
] %}

<div{{attributes.addClass(classes)}}>
  {% for item in items %}
    <div>
      {{ item.content }}
    </div>
  {% endfor %}
</div>
```

## Styling

The component includes:
- Flexbox layout for items
- Flex wrap support
- Consistent gap between items (0.5rem)
- Support for custom classes via attributes
