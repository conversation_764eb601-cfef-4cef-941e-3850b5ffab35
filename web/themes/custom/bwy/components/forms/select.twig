{#
/**
 * @file
 * Default theme implementation for a select element.
 *
 * Available variables:
 * - attributes: HTML attributes for the <select> tag.
 * - options: The <option> element children.
 *
 * @see template_preprocess_select()
 *
 * @ingroup themeable
 */
#}
{% set classes = [
    'select',
    'body-2',
    'w-full',
    'p-3',
    'border',
    'border-solid',
    'border-border-main',
    'font-medium',
    'rounded-lg',
    'transition-colors',
    'duration-300',
    'ease-in-out',
    'text-black',
    attributes.hasClass('error') ? 'invalid:border-error',
    attributes.disabled or attributes.readonly ? 'cursor-not-allowed',
  ]
%}
<div class="relative form-type-select__wrapper">
  <select{{ attributes.addClass(classes) }}>
    {% for option in options %}
      {% if option.type == 'optgroup' %}
        <optgroup label="{{ option.label }}">
          {% for sub_option in option.options %}
            <option value="{{ sub_option.value }}" {{ sub_option.selected ? ' selected="selected"' }}>{{ sub_option.label }}</option>
          {% endfor %}
        </optgroup>
      {% elseif option.type == 'option' %}
        <option value="{{ option.value }}" {{ option.selected ? ' selected="selected"' }}>{{ option.label }}</option>
      {% endif %}
    {% endfor %}
  </select>
</div>
