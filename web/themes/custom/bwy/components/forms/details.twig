{% set wrapper_classes = [
  'p-5',
  'border',
  'border-border-main',
  'rounded-lg',
  'bg-white'
] %}

{%
  set title_classes = [
    required ? 'js-form-required',
    required ? 'form-required',
    'heading-3',
    'mb-4',
  ]
%}

{% if title_attributes is not defined %}
  {% set title_attributes = create_attribute() %}
{% endif %}

<div{{attributes.addClass(wrapper_classes)}}>
  {%- if title -%}
    <div {{title_attributes.addClass(title_classes)}}>{{ title }}</div>
  {%- endif -%}

  <div class="flex flex-col gap-4">
    {% if errors %}
      <div>
        {{ errors }}
      </div>
    {% endif %}

    {{ description }}
    {{ children }}
    {{ value }}
  </div>
</div>
