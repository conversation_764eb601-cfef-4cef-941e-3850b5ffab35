{#
/**
 * @file
 * Default theme implementation for a 'textarea' #type form element.
 *
 * Available variables
 * - wrapper_attributes: A list of HTML attributes for the wrapper element.
 * - attributes: A list of HTML attributes for the <textarea> element.
 * - resizable: An indicator for whether the textarea is resizable.
 * - required: An indicator for whether the textarea is required.
 * - value: The textarea content.
 *
 * @see template_preprocess_textarea()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    resizable ? 'resize-' ~ resizable,
    required ? 'required',
    'form-textarea',
    'body-2',
    'w-full',
    'p-3',
    'border',
    'border-solid',
    'border-border-main',
    'font-medium',
    'rounded-lg',
    'transition-colors',
    'duration-300',
    'ease-in-out',
    'text-black',
    attributes.hasClass('error') ? 'invalid:border-error invalid:border invalid:text-error invalid:placeholder:text-error',
  ]
%}

<div{{ wrapper_attributes }}>
  <textarea{{ attributes.addClass(classes) }}>{{ value }}</textarea>
</div>
