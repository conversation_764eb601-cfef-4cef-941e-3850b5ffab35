{#
/**
 * @file
 * Theme override for an 'input' #type form element.
 *
 * Available variables:
 * - attributes: A list of HTML attributes for the input element.
 * - children: Optional additional rendered elements.
 *
 * @see template_preprocess_input()
 */
#}
{% set classes = [
    'input-submit',
    'body-3',
    'inline-flex',
    'items-center',
    'justify-center',
    'px-6',
    'py-2.5',
    'gap-2',
    'rounded-full-99',
    'transition-colors',
    'focus:outline-none',
    'focus:ring-2',
    'bg-blue-600',
    'text-white',
    'hover:bg-blue-700',
    'focus:ring-blue-700'
  ]
%}

<input{{ attributes.addClass(classes) }} />{{ children }}
