{#
/**
 * @file
 * Theme override for an 'input' #type form element.
 *
 * Available variables:
 * - attributes: A list of HTML attributes for the input element.
 * - children: Optional additional rendered elements.
 *
 * @see template_preprocess_input()
 */
#}
{% set classes = [
    'input',
    'body-2',
    attributes['type'] != 'checkbox' ? 'w-full' : '',
    'p-3',
    'border',
    'border-solid',
    'border-border-main',
    'font-medium',
    'rounded-lg',
    'transition-colors',
    'duration-300',
    'ease-in-out',
    'text-black',
    attributes.hasClass('error') ? 'invalid:border-error invalid:text-error',
    attributes.disabled or attributes.readonly ? 'cursor-not-allowed',
  ]
%}

<input{{ attributes.addClass(classes) }} />{{ children }}
