{#
/**
 * @file
 * Company Header component.
 *
 * Displays company header with logo, name, description and company information.
 */
#}
{% if company_name is not empty %}
  <div class="mb-8" role="region" aria-label="{{ 'Company header'|t }}">
    {# Logo and Top Company badge #}
    <div class="flex items-center justify-between mb-8">
      {% if company_logo is not empty %}
        <div class="w-16 h-16 flex-shrink-0" role="img" aria-label="{{ company_name }}">
          {{ company_logo }}
        </div>
      {% endif %}
      {% if is_top_company is not empty %}
        <div class="flex-shrink-0">
          <span class="px-3 py-1 text-xs font-normal bg-yellow-700/35 text-black" role="status" aria-label="{{ 'This is a top company'|t }}">
            {{ "Top company"|t }}
          </span>
        </div>
      {% endif %}
    </div>

    <div class="grid md:grid-cols-12 gap-8 md:gap-16">
      {# Company Title and Description #}
      <div class="col-span-9">
        <h2{{ title_attributes.addClass('heading-3 mb-3') }}>{{ company_name }}</h2>
        {% if company_description is not empty %}
          <div class="text-gray-600" aria-label="{{ 'Company description'|t }}">{{ company_description }}</div>
        {% endif %}
      </div>

      {# Company Information #}
      {% if company_address is not empty or company_website is not empty or company_social_media is not empty %}
        <div class="col-span-3">
          {% include "@bwy/components/company_info/company_info.twig" with {
            address: company_address,
            website: company_website,
            social_media: company_social_media
          } %}
        </div>
      {% endif %}
    </div>
  </div>
{% endif %}
