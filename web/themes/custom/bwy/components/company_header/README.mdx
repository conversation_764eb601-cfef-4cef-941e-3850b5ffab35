# Company Header Component

A component that displays company information in a header format, including logo, name, description, and additional company information.

## Usage

```twig
{% include "@bwy/components/company_header/company_header.twig" with {
  company_name: 'Company Name',
  company_logo: logo_render_array,
  company_description: 'Company description text',
  is_top_company: true,
  company_address: address_render_array,
  company_website: website_render_array,
  company_social_media: social_media_render_array
} %}
```

## Props

| Name                 | Type         | Description                         | Required |
| -------------------- | ------------ | ----------------------------------- | -------- |
| company_name         | string       | The name of the company             | Yes      |
| company_logo         | render array | Company logo image                  | No       |
| company_description  | string       | Company description text            | No       |
| is_top_company       | boolean      | Whether to show "Top company" badge | No       |
| company_address      | render array | Company address information         | No       |
| company_website      | render array | Company website link                | No       |
| company_social_media | render array | Company social media links          | No       |

## Accessibility (WCAG AA Compliance)

### Color Contrast

- Text color uses a contrast ratio of at least 4.5:1 for normal text (gray-600, gray-900)
- "Top company" badge uses black text on yellow background with sufficient contrast

### Structure

- Uses semantic HTML with proper heading hierarchy (h2)
- Logo image must include alt text in the render array
- Company name is marked as h2 for proper document outline

### Screen Readers

- Important content is not conveyed through color alone
- "Top company" badge text is translatable for multi-language support
- Social media links should include descriptive text (handled by company_info component)

### Keyboard Navigation

- All interactive elements must be focusable and have visible focus indicators
- Logical tab order follows visual layout

## Best Practices

1. Always provide alt text for company logo
2. Ensure text content is translatable
3. Maintain proper heading hierarchy in the context where this component is used
4. Test with screen readers to ensure proper content announcement
5. Verify keyboard navigation works as expected

## Dependencies

- Requires the company_info component for displaying detailed company information
- Uses Tailwind CSS for styling
