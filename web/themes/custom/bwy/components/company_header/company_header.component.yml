name: 'Company Header'
description: 'Displays company header with logo and top company badge'
props:
  company_name:
    type: field
    required: true
    description: 'The name field of the company'
  company_description:
    type: field
    required: false
    description: 'The description field of the company'
  company_logo:
    type: field
    required: false
    description: 'The logo field of the company'
  is_top_company:
    type: field
    required: false
    description: 'The field indicating if this is a top company'
  company_address:
    type: field
    required: false
    description: 'The address field of the company'
  company_website:
    type: field
    required: false
    description: 'The website field of the company'
  company_social_media:
    type: field
    required: false
    description: 'The social media links field of the company'
