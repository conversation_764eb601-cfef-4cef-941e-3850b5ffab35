$schema: https://git.drupalcode.org/project/drupal/-/raw/10.1.x/core/modules/sdc/src/metadata.schema.json
name: Field Description
status: experimental
description: Field description component with typography styling.
props:
  type: object
  properties:
    items:
      type: array
      title: Items
      description: List of all the field items.
    field_name:
      type: string
      title: Field Name
      description: The name of the field.
    field_type:
      type: string
      title: Field Type
      description: The type of the field.
    field_classes:
      type: array
      items:
        type: string
      title: Field Classes
      default: ['field']
      description: Base classes for the field wrapper.
    field_utility_classes:
      type: array
      items:
        type: string
      title: Field Utility Classes
      default: []
      description: Additional classes to be added to the field wrapper.
    custom_classes:
      type: string
      title: Custom Classes
      default: ''
      description: Space-separated custom classes to be added to the field wrapper.
    field_title_utility_classes:
      type: array
      items:
        type: string
      title: Field Title Utility Classes
      default: []
      description: Additional classes to be added to the field title wrapper.
    title_attributes:
      type: Drupal\Core\Template\Attribute
    attributes:
      type: Drupal\Core\Template\Attribute
