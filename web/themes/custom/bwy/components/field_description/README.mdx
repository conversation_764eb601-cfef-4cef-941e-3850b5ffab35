# Field Description Component

A reusable component that displays formatted text content with enhanced typography using Tailwind's Typography plugin. The component automatically applies field classes for consistent text formatting.

## Features

- Displays field items with proper Drupal field structure
- Configurable field classes and custom classes
- Automatic field name and type class generation
- Semantic HTML structure

## Usage

```twig
{# Basic usage #}
{% include 'bwy:field_description' with {
  items: items,
  attributes: attributes,
  field_name: field_name,
  field_type: field_type
} only %}

{# With custom classes #}
{% include 'bwy:field_description' with {
  items: items,
  attributes: attributes,
  field_name: field_name,
  field_type: field_type,
  custom_classes: 'my-spacing special-text'
} only %}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `items` | array | Yes | List of field items to be displayed |
| `attributes` | Attribute | Yes | HTML attributes for the field items |
| `field_name` | string | Yes | The name of the field |
| `field_type` | string | Yes | The type of the field |
| `field_utility_classes` | array | No | Additional utility classes for the field wrapper |
| `custom_classes` | string | No | Space-separated custom classes to be added to the field wrapper |

## Structure

The component generates a simple but semantic structure:

```html
{% for item in items %}
  <div class="field field--name-[name] field--type-[type] full-text [utility-classes] [custom-classes]">
    {{ item.content }}
  </div>
{% endfor %}
```

## Default Classes

The component automatically applies these base classes:
- `field`: Base Drupal field class
- `field--name-{field_name}`: Field name specific class
- `field--type-{field_type}`: Field type specific class
- `full-text`: Component specific styling class

## Customization

You can customize the component in several ways:

1. **Field Utility Classes**: Add Drupal/theme specific utility classes
```twig
field_utility_classes: ['text-center', 'my-4']
```

2. **Custom Classes**: Add space-separated custom classes
```twig
custom_classes: 'special-content extra-spacing'
```

3. **Attributes**: Add custom attributes to the field items
```twig
attributes: create_attribute().addClass(['custom-class'])
```

## Best Practices

1. Use semantic class names that describe their purpose
2. Keep the content properly formatted
3. Use custom classes sparingly and prefer utility classes for common styling
4. Maintain proper heading hierarchy in the content
5. Consider accessibility when adding custom styles

## Related Components

- `field` - Base field component
- Other field-specific components in the theme
