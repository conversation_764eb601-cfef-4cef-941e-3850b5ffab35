{#
/**
 * @file
 * Template for a field description component.
 *
 * Available variables:
 * - items: List of all the field items. Each item contains:
 *   - content: The field item content.
 *   - attributes: HTML attributes for each item.
 * - field_name: The name of the field.
 * - field_type: The type of the field.
 * - field_utility_classes: Additional classes to be added to the field wrapper.
 * - custom_classes: Space-separated custom classes to be added to the field wrapper.
 * - attributes: HTML attributes for the containing element.
 */
#}
{%
  set field_classes = [
    'field',
    'field-description',
    'field--name-' ~ field_name|clean_class,
    'field--type-' ~ field_type|clean_class,
    'full-text',
    custom_classes ?: ''
  ]|merge(field_utility_classes ?: [])
%}

<div {{ attributes.addClass(field_classes) }}>
  {% for item in items %}
    {{ item.content }}
  {% endfor %}
</div>
