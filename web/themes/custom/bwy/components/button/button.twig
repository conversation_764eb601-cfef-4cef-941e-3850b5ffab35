{#
/**
 * @file
 * Button component.
 */
#}

{% set button_classes = [
  'body-3',
  'inline-flex',
  'items-center',
  'justify-center',
  'gap-2',
  'rounded-full-99',
  'transition-colors',
  'focus:outline-none',
  'focus:ring-2',
  button_type == 'primary' ? 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-700' : null,
  button_type == 'secondary' ? 'bg-white text-blue-600 border-2 border-blue-600 hover:bg-blue-500 focus:ring-blue-500' : null,
  button_type == 'primary_dark' ? 'bg-gray-900 text-white hover:bg-gray-800 focus:ring-gray-500' : null,
  button_type == 'link' ? 'link-text no-underline text-blue-600 hover:text-blue-700 hover:underline focus:ring-blue-500 py-1' : 'px-6 py-2.5',
  disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : null
] %}

{# Initialize attributes if not provided #}
{% if button_attributes is not defined %}
  {% set button_attributes = create_attribute() %}
{% endif %}

{# Ensure we're working with a Drupal Attribute object #}
{% if button_attributes is not empty and button_attributes is not iterable %}
  {% set button_attributes = create_attribute(button_attributes) %}
{% endif %}

{# Add accessibility attributes #}
{% if aria_label is not empty %}
  {% set button_attributes = button_attributes.setAttribute('aria-label', aria_label) %}
{% endif %}

{% if disabled %}
  {% set button_attributes = button_attributes.setAttribute('aria-disabled', 'true') %}
  {% if tag == 'button' %}
    {% set button_attributes = button_attributes.setAttribute('disabled', 'disabled') %}
  {% endif %}
{% endif %}

{# If using an anchor tag, ensure it has a role of button when styled as one #}
{% if tag == 'a' and button_type != 'link' %}
  {% set button_attributes = button_attributes.setAttribute('role', 'button') %}
{% endif %}

<{{tag|default('a')}}{{button_attributes.addClass(button_classes)}}>
  <span class="inline-block">{{ text }}</span>
  {% if with_icon %}
    {% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
      icon_name: 'arrow_right',
      classes: button_type == 'link' ? 'text-blue-600' : null,
      aria_label: false
    } only %}
  {% endif %}
</{{tag|default('a')}}>
