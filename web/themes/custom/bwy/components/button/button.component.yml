name: Button
description: A versatile button component with different styles and optional icon
props:
  text:
    type: string
    required: true
    description: The button text
  button_type:
    type: string
    required: false
    default: primary
    enum:
      - primary
      - secondary
      - primary_dark
      - link
    description: The type of button to display
  tag:
    type: string
    required: false
    default: a
    description: HTML tag to use (a or button)
  button_attributes:
    type: object
    required: false
    description: Drupal attributes object
  with_icon:
    type: boolean
    required: false
    default: false
    description: Whether to show the arrow icon
  aria_label:
    type: string
    required: false
    description: Accessible label for the button (if different from text)
  disabled:
    type: boolean
    required: false
    default: false
    description: Whether the button is disabled
