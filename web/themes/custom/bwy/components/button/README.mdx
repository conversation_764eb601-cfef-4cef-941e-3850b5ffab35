# Button Component

A versatile button component that supports multiple styles and optional icon integration. The component is WCAG 2.1 AA compliant.

## Accessibility Features

- Sufficient color contrast (meets WCAG AA 4.5:1 for normal text)
- Focus indicators for keyboard navigation
- Proper ARIA attributes for different states
- Screen reader support
- Keyboard navigation support

## Usage

```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Click me',
  button_type: 'primary',
  with_icon: true
} %}
```

## Properties

| Name | Type | Default | Required | Description |
|------|------|---------|----------|-------------|
| text | string | - | Yes | The button text |
| button_type | string | 'primary' | No | Button style (primary, secondary, primary_dark, link) |
| tag | string | 'a' | No | HTML tag to use (a or button) |
| button_attributes | object | - | No | Drupal attributes object |
| with_icon | boolean | false | No | Whether to show the arrow icon |
| aria_label | string | - | No | Accessible label (if different from text) |
| disabled | boolean | false | No | Whether the button is disabled |

## Examples

### Primary Button
```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Primary Button',
  button_type: 'primary'
} %}
```

### Secondary Button
```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Secondary Button',
  button_type: 'secondary'
} %}
```

### Primary Dark Button
```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Dark Button',
  button_type: 'primary_dark'
} %}
```

### Link Button with Icon
```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Learn More',
  button_type: 'link',
  with_icon: true
} %}
```

### Button with Custom Attributes
```twig
{# Create button attributes #}
{% set url = 'https://example.com' %}
{% set my_button_attributes = create_attribute() %}
{% set my_button_attributes = my_button_attributes
  .addClass(['my-custom-class', 'another-class'])
  .setAttribute('href', url)
  .setAttribute('target', '_blank')
%}

{# Pass attributes to button #}
{% include '@bwy/components/button/button.twig' with {
  text: 'Custom Button',
  button_type: 'primary',
  button_attributes: my_button_attributes
} %}
```

### Accessible Button Examples

#### Button with Custom Aria Label
```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Read',
  aria_label: 'Read more about our services',
  button_type: 'primary'
} %}
```

#### Disabled Button
```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Submit',
  button_type: 'primary',
  disabled: true
} %}
```

#### Submit Button (using button tag)
```twig
{% include '@bwy/components/button/button.twig' with {
  text: 'Submit Form',
  button_type: 'primary',
  tag: 'button',
  button_attributes: create_attribute().setAttribute('type', 'submit')
} %}
```

### Button with Data Attributes
```twig
{% set my_button_attributes = create_attribute()
  .setAttribute('data-tracking', 'homepage-cta')
  .setAttribute('data-analytics', 'clicked')
%}

{% include '@bwy/components/button/button.twig' with {
  text: 'Track Me',
  button_type: 'primary',
  button_attributes: my_button_attributes
} %} 