# Job Post Information Component

A flexible component for displaying job-related information in a grid layout with proper accessibility.

## Usage

```twig
{% include '@bwy/components/job_post_information/job_post_information.twig' with {
    fields: [
        field_work_type,
        field_place_of_work,
        salary_range
    ]
} %}
```

## Props

| Name   | Type  | Description                                  | Required |
|--------|-------|----------------------------------------------|----------|
| fields | Array | Array of Drupal field render arrays          | Yes      |

## Special Field Types

### Salary Range
Fields with `#type: 'salary_range'` receive special formatting with a "Monthly salary" heading.

## Grid Layout

- Mobile: 1 column
- Tablet: 2 columns
- Desktop: 3 columns

## Accessibility Features

- Semantic list structure using roles
- Proper heading hierarchy for field labels
- Responsive grid maintains readability
- Clear visual hierarchy
- Sufficient spacing for readability
- Meets WCAG 2.1 AA standards

## Dependencies

- Tai<PERSON><PERSON> CSS for styling and grid layout
