{#
/**
 * @file
 * Job Post Information component.
 *
 * Available variables:
 * - fields: An array of field render arrays to display.
 *   Special handling for salary_range type fields.
 */
#}
{% if fields %}
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
    {% for field in fields %}
      {% if field[0] is defined %}
        {% if field['#type'] == 'salary_range' %}
          <div role="listitem">
            <h2 class="mb-4.5 body-1 font-bold">{{ "Monthly salary"|t }}</h2>
            <div>
              {{ field }}
            </div>
          </div>
        {% else %}
          <div role="listitem">
            {{ field }}
          </div>
        {% endif %}
      {% endif %}
    {% endfor %}
  </div>
{% endif %}
