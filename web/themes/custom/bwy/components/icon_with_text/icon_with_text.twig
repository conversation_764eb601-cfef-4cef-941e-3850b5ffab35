{# Icon with text component #}

{% set wrapper_classes = [
  'flex',
  'items-center',
  'gap-2',
  classes ?: ''
] %}

{# Set default values for accessibility #}
{% set is_decorative = is_decorative ?? true %}
{% set aria_label = aria_label ?? null %}

{% if icon_name is not empty %}
  {% set icon %}{% include '@bwy/src/svg/' ~ icon_name ~ '.svg' %}{% endset %}
{% endif %}

{% if wrapper_attributes is not defined %}
  {% set wrapper_attributes = create_attribute() %}
{% endif %}

{# Create separate attributes for the icon span #}
{% set icon_attributes = create_attribute() %}

{# Add accessibility attributes to icon span #}
{% if is_decorative %}
  {% set icon_attributes = icon_attributes.setAttribute('aria-hidden', 'true') %}
{% endif %}

{% if text is empty and aria_label is not empty %}
  {% set icon_attributes = icon_attributes.setAttribute('aria-label', aria_label) %}
  {% set icon_attributes = icon_attributes.setAttribute('role', 'img') %}
{% endif %}

<span{{ wrapper_attributes.addClass(wrapper_classes) }}>
  {% if icon is defined %}
    <span{{ icon_attributes.addClass('inline-flex shrink-0') }}>
      {{ icon }}
    </span>
  {% endif %}
  {% if text is not empty %}
    <span class="inline-block">
      {{ text }}
    </span>
  {% endif %}
</span>
