$schema: https://git.drupalcode.org/project/drupal/-/raw/10.1.x/core/modules/sdc/src/metadata.schema.json
name: Icon with text
description: 'A reusable icon component that can display an SVG icon with optional text.'
props:
  icon_name:
    type: string
    required: false
    description: 'The name of the icon to display'
  text:
    type: string
    required: false
    description: 'Optional text to display next to the icon'
  classes:
    type: string
    required: false
    description: 'Additional classes to be added to the icon wrapper'
  attributes:
    type: Drupal\Core\Template\Attribute
    required: false
    description: 'HTML attributes for the icon wrapper, including classes for styling'
  is_decorative:
    type: boolean
    required: false
    description: 'Whether the icon is decorative (true) or meaningful (false). Decorative icons will be hidden from screen readers.'
  aria_label:
    type: string
    required: false
    description: 'Accessible label for the icon when used without text. Required for meaningful icons without text.'
variants:
  decorative:
    title: Decorative Icon
    description: 'Icon that is purely decorative and hidden from screen readers'
  meaningful:
    title: Meaningful Icon
    description: 'Icon that conveys meaning and includes proper accessibility attributes'
  icon_with_text:
    title: Icon with Text
    description: 'Icon accompanied by descriptive text'
