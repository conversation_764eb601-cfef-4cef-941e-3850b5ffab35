# Icon With Text Component

A reusable component that displays an SVG icon with optional accompanying text. The component is designed to be flexible and can be used in various contexts throughout the application.

## Features

- Displays SVG icons from the theme's SVG directory
- Optional text display alongside the icon
- Flexible styling through CSS classes
- Supports custom HTML attributes
- Built-in Flexbox layout for proper alignment
- WCAG AA compliant accessibility features
- Proper handling of decorative vs meaningful icons

## Usage

```twig
{# Decorative icon (hidden from screen readers) #}
{% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
  icon_name: 'calendar',
  is_decorative: true
} %}

{# Meaningful icon without text (requires aria-label) #}
{% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
  icon_name: 'calendar',
  is_decorative: false,
  aria_label: 'Calendar'
} %}

{# Icon with text (automatically handles accessibility) #}
{% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
  icon_name: 'calendar',
  text: 'Event Date',
  classes: 'text-gray-600'
} %}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `icon_name` | string | No | The name of the SVG icon file (without extension) from the theme's SVG directory |
| `text` | string | No | Text to display next to the icon |
| `classes` | string | No | Additional CSS classes to apply to the wrapper element |
| `attributes` | Attribute | No | Drupal attributes object for additional HTML attributes |
| `is_decorative` | boolean | No | Whether the icon is decorative (true) or meaningful (false). Defaults to true |
| `aria_label` | string | No | Accessible label for the icon when used without text. Required for meaningful icons without text |

## Accessibility Features

The component follows WCAG AA accessibility guidelines:

1. **Decorative Icons**
   - Icon span has `aria-hidden="true"` to hide from screen readers
   - Used when the icon is purely visual or redundant

2. **Meaningful Icons**
   - Icon span has `aria-label` when used without text
   - Icon span has `role="img"` for semantic meaning
   - Properly announced by screen readers

3. **Icons with Text**
   - Icon span has `aria-hidden="true"`
   - Text remains accessible and readable
   - Proper spacing between icon and text

4. **General Features**
   - Sufficient color contrast (4.5:1 ratio)
   - Proper focus indicators
   - Semantic HTML structure
   - Flexible text sizing
   - Screen reader friendly

## Structure

The component uses a semantic HTML structure with proper accessibility attributes:

```html
<!-- Decorative icon -->
<span class="flex items-center gap-2">
  <span class="inline-flex shrink-0" aria-hidden="true">[icon svg]</span>
</span>

<!-- Meaningful icon -->
<span class="flex items-center gap-2">
  <span class="inline-flex shrink-0" role="img" aria-label="Calendar">[icon svg]</span>
</span>

<!-- Icon with text -->
<span class="flex items-center gap-2">
  <span class="inline-flex shrink-0" aria-hidden="true">[icon svg]</span>
  <span class="inline-block">Event Date</span>
</span>
```

## Available Icons

Icons should be placed in the `@bwy/src/svg/` directory. Currently available icons:
- `arrow_forward.svg`
- `calendar.svg`
- `chevron_right_filled.svg`

## Best Practices

1. Always specify whether an icon is decorative or meaningful using `is_decorative`
2. Provide `aria-label` for meaningful icons without text
3. Use semantic and descriptive text when adding accompanying text
4. Maintain consistent spacing through utility classes
5. Ensure SVG icons are optimized and properly formatted
6. Test with screen readers to verify accessibility

## Styling

The component uses Tailwind CSS classes for layout:
- Wrapper: `flex items-center gap-2`
- Icon span: `inline-flex shrink-0`
- Text span: `inline-block`

Additional styling can be added through the `classes` prop.
