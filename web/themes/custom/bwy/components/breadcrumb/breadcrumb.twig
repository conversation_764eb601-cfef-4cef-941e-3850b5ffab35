{#
/**
 * Breadcrumb Navigation Component
 */
#}

{% if breadcrumb and not is_front %}
  <nav class="flex" aria-label="{{ 'Breadcrumb'|t }}">
    <ol class="inline-flex flex-wrap items-center" role="list">
      {% for item in breadcrumb %}
        {% if item.text %}
          <li class="inline-flex items-center">
            {% if item.url %}
              <a href="{{ item.url }}" class="text-sm font-medium text-white-30 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2" {% if loop.first %} aria-label="{{ 'Go to home page'|t }}" {% endif %}>
                {{ item.text }}
              </a>
            {% else %}
              <span class="text-sm font-medium" aria-current="page">{{ item.text }}</span>
            {% endif %}
            {% if not loop.last %}
              {% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
            icon_name: 'chevron_right_filled',
            text: '',
            classes: 'mx-2 text-gray-400',
            icon_attributes: create_attribute().setAttribute('aria-hidden', 'true')
          } only %}
            {% endif %}
          </li>
        {% endif %}
      {% endfor %}
    </ol>
  </nav>
{% endif %}
