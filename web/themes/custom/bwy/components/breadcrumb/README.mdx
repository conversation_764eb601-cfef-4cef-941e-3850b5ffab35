# Breadcrumb Component

A navigation component that shows the current page's location within a navigational hierarchy. The component is responsive and will wrap items to the next line on mobile devices when needed.

## Features

- Responsive design with proper wrapping on mobile devices
- Automatic current page title inclusion
- Accessible navigation with proper ARIA attributes
- Icon integration with chevron separators
- Proper focus states for interactive elements
- Hidden on front page

## Usage

```twig
{# The component automatically gets breadcrumb data from Dr<PERSON>al #}
{% include '@bwy/breadcrumb/breadcrumb.twig' %}
```

## Variables

The component expects the following variables from <PERSON>upal:

- `breadcrumb` (array): Array of breadcrumb items provided by Drupal
  - `text` (string): The text to display for the breadcrumb item
  - `url` (string|null): The URL that the breadcrumb item links to. Will be null for the current page.
- `is_front` (boolean): Whether the current page is the front page. When true, the breadcrumb navigation is hidden.
- `attributes` (object, optional): Drupal attributes object for additional classes or data attributes

## Accessibility Features

- Uses semantic HTML5 `<nav>` with `aria-label="Breadcrumb"`
- Proper heading hierarchy maintained
- Ordered list (`<ol>`) for sequential navigation
- Current page marked with `aria-current="page"`
- Color contrast ratio meets WCAG AA (4.5:1 for normal text)
- Interactive elements are keyboard accessible
- Focus indicators are visible
- Text is resizable without loss of functionality
- Screen reader friendly structure
- Icons are properly hidden from screen readers
