{#
/**
 * @file
 * Company Information component.
 *
 * Displays company contact information including address, website, and social media links.
 */
#}
{% if address is not empty or website is not empty or social_media is not empty %}
  <div class="flex flex-col gap-4.5" role="complementary" aria-labelledby="company-info-title">
    <h3 id="company-info-title" class="body-1 font-bold mb-4.5">{{ "Information about the company"|t }}</h2>
    {% if address is not empty or website is not empty %}
      <div class="flex flex-col gap-3.5" role="group" aria-label="{{ 'Contact information'|t }}">
        {% if address is not empty %}
          <div class="company-address">
            {{ address }}
          </div>
        {% endif %}

        {% if website is not empty %}
          <div class="company-website">
            {{ website }}
          </div>
        {% endif %}
      </div>
    {% endif %}

    {% if social_media is not empty %}
      <div role="group" aria-label="{{ 'Social media links'|t }}">
        {{ social_media }}
      </div>
    {% endif %}
  </div>
{% endif %}
