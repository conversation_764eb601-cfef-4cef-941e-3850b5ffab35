# Company Info Component

A component that displays company contact information including address, website, and social media links in a structured format.

## Usage

```twig
{% include "@bwy/components/company_info/company_info.twig" with {
  address: address_render_array,
  website: website_render_array,
  social_media: social_media_render_array
} %}
```

## Props

| Name         | Type         | Description                 | Required |
| ------------ | ------------ | --------------------------- | -------- |
| address      | render array | Company address information | No       |
| website      | render array | Company website link        | No       |
| social_media | render array | Company social media links  | No       |

## Accessibility (WCAG AA Compliance)

### Structure

- Uses semantic HTML with proper heading hierarchy (h2 for section title)
- Information is organized in logical sections with proper spacing
- Uses semantic grouping with `<div>` elements and meaningful class names

### Screen Readers

- Section title "Information about the company" is translatable
- Address information should be properly marked up (handled by address render array)
- Website and social media links must have descriptive text
- Links should indicate if they open in a new window/tab

### Keyboard Navigation

- All links must be focusable
- Focus order follows logical document flow
- Focus indicators must be visible (handled by Tailwind CSS)
- Links should be properly spaced for touch targets (gap-3.5 and gap-4.5 classes)

### Content Structure

- Information is grouped logically for easy comprehension
- Consistent spacing improves visual hierarchy
- Empty states are handled gracefully (component only renders when content exists)

## Best Practices

1. Ensure all links have descriptive text (not just "click here" or "website")
2. Maintain proper heading hierarchy in the context where this component is used
3. Test with screen readers to ensure proper content announcement
4. Verify keyboard navigation works as expected
5. Ensure all text content is translatable
6. Test component with various combinations of optional props

## Dependencies

- Uses Tailwind CSS for styling and spacing
- Expects properly formatted render arrays for address, website, and social media content

## Related Components

- Often used within the Company Header component
- May be used independently where company contact information needs to be displayed
