# Job Post Content Component

A comprehensive component for displaying job posting content with proper structure and accessibility.

## Usage

```twig
{% include '@bwy/components/job_post_content/job_post_content.twig' with {
    content: content,
    salary: salary_range,
    job_title: label
} %}
```

## Props

| Name      | Type     | Description                                     | Required |
|-----------|----------|-------------------------------------------------|----------|
| content   | Object   | Drupal content object containing all field values | Yes      |
| job_title | String   | The title of the job post                        | Yes      |
| salary    | Object   | Salary range information render array            | Yes      |

## Structure

The component organizes job post information into three main sections:
1. Job Details (work type, location, salary)
2. Job Description
3. Additional Information (benefits, requirements, etc.)

## Accessibility Features

- Uses semantic HTML5 elements (`<article>`, `<section>`)
- Proper heading hierarchy (h1 for job title)
- ARIA labels for sections
- Responsive layout
- Clear content organization
- Meets WCAG 2.1 AA standards

## Dependencies

- job_post_information component
- Tai<PERSON>wind CSS for styling
