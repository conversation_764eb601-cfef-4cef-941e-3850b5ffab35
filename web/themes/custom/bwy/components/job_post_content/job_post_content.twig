{#
/**
 * @file
 * Job Post Content component.
 *
 * Available variables:
 * - content: The Drupal content object containing all field values.
 * - job_title: The title of the job post.
 * - salary: The salary information render array.
 */
#}
<article class="flex flex-col gap-8 md:gap:8" role="article">
  <h1 class="heading-3">{{ job_title }}</h1>

  {# Job Details Section #}
  <section aria-label="{{ 'Job Details'|t }}">
    {% include '@bwy/components/job_post_information/job_post_information.twig' with {
      fields: [
        content.field_work_type,
        content.field_place_of_work,
        salary
      ]
    } %}
  </section>

  {# Job Description Section #}
  {% if content.field_description_requiremens %}
    <section aria-label="{{ 'Job Description'|t }}">
      <div class="max-w-none">
        {{ content.field_description_requiremens }}
      </div>
    </section>
  {% endif %}

  {# Job Additional Information Section #}
  <section aria-label="{{ 'Additional Information'|t }}">
    {% include '@bwy/components/job_post_information/job_post_information.twig' with {
      fields: [
        content.field_professional_field,
        content.field_languages,
        content.field_education,
        content.field_working_hours,
        content.field_annual_leave,
        content.field_annual_leave_days,
        content.field_remuneration_and_bonuses,
        content.field_health_and_insurance,
        content.field_development_and_training,
        content.field_allocated_budget,
        content.field_food_and_beverage,
        content.field_food_voucher_amount,
        content.field_sport_and_wellness,
        content.field_relocation_and_transport,
        content.field_family_and_social_support,
        content.field_working_environment,
        content.field_additional_benefits,
        content.field_other_benefit
      ]
    } %}
  </section>
</article>
