{#
/**
 * @file
 * Template for a Event Card component.
 *
 * Available variables:
 * - slots.image: Featured image for the event
 * - slots.title: Event title
 * - slots.date: Event start-end date
 * - slots.place: Event place
 * - slots.address: Event address
 * - slots.read_more_link: Link to the full event page
 * - url: URL to the full event page
 * - heading_level: Heading level for the title (default: 3)
 * - read_more_text: Text for read more link (default: 'Read more')
 * - category_text: Category badge text for featured variant
 * - variant: Component variant ('default' or 'featured')
 * - place_label: Label for the place
 * - address_label: Label for the address
 * - current_event: Whether the event is currently ongoing
 * - past_event: Whether the event has already ended
 */
#}

{%
  set card_classes = [
    'card',
    url ? 'card--hover',
    'event-card',
    'event-card--' ~ variant,
  ]
%}

<div {{ attributes.addClass(card_classes) }}>
  {% if variant == 'featured' %}
    <div class="md:flex">
  {% endif %}

  {# Image section for featured #}
  {% if slots.image %}
    <div class="event-image relative{{ variant == 'featured' ? ' md:w-1/2' : '' }}">
      {%
        set event_image_inner_classes = [
          'w-full overflow-hidden h-50',
          variant == 'featured' ? 'md:min-h-88.5',
        ]
      %}
      <div{{ create_attribute().addClass(event_image_inner_classes) }}>
        {{ slots.image }}
      </div>
      {# Optional overlay gradient #}
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
    </div>
  {% endif %}

  {%
    set event_content_classes = [
      'event-content',
      variant == 'featured' ? 'p-3 md:p-6 md:w-1/2 relative',
      variant == 'default' or not variant ? 'p-3 md:p-5',
    ]
  %}
  <div{{ create_attribute().addClass(event_content_classes) }}>
    {% if variant == 'featured' %}
      {# Category badge for featured #}
      <div class="category-badge mb-2">
        <span class="inline-block text-black bg-pink-600/35 link-text p-2 pr-5 rounded-l-lg [clip-path:polygon(0_0,100%_0,calc(100%-12px)_100%,0_100%)]">
          {{ category_text|default(current_event ? 'Last hours of the event'|t : 'Upcoming event'|t) }}
        </span>
      </div>
    {% endif %}

    {# Event location #}
    {% if slots.location %}
      <div class="event-location body-2 flex items-center text-text-main gap-1">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
          <mask id="mask0_383_9389" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
            <rect width="24" height="24"/>
          </mask>
          <g mask="url(#mask0_383_9389)">
          <path d="M12.025 16.5C13.675 15.2833 14.9167 14.0625 15.75 12.8375C16.5833 11.6125 17 10.3833 17 9.15C17 7.45 16.4583 6.16667 15.375 5.3C14.2917 4.43333 13.1667 4 12 4C10.8333 4 9.70833 4.43333 8.625 5.3C7.54167 6.16667 7 7.45 7 9.15C7 10.2667 7.40833 11.4292 8.225 12.6375C9.04167 13.8458 10.3083 15.1333 12.025 16.5ZM12 19C9.65 17.2667 7.89583 15.5833 6.7375 13.95C5.57917 12.3167 5 10.7167 5 9.15C5 7.96667 5.2125 6.92917 5.6375 6.0375C6.0625 5.14583 6.60833 4.4 7.275 3.8C7.94167 3.2 8.69167 2.75 9.525 2.45C10.3583 2.15 11.1833 2 12 2C12.8167 2 13.6417 2.15 14.475 2.45C15.3083 2.75 16.0583 3.2 16.725 3.8C17.3917 4.4 17.9375 5.14583 18.3625 6.0375C18.7875 6.92917 19 7.96667 19 9.15C19 10.7167 18.4208 12.3167 17.2625 13.95C16.1042 15.5833 14.35 17.2667 12 19ZM12 11C12.55 11 13.0208 10.8042 13.4125 10.4125C13.8042 10.0208 14 9.55 14 9C14 8.45 13.8042 7.97917 13.4125 7.5875C13.0208 7.19583 12.55 7 12 7C11.45 7 10.9792 7.19583 10.5875 7.5875C10.1958 7.97917 10 8.45 10 9C10 9.55 10.1958 10.0208 10.5875 10.4125C10.9792 10.8042 11.45 11 12 11Z"/>
          </g>
        </svg>

        {{ slots.location }}
      </div>
    {% endif %}

    {# Title #}
    {% if slots.title %}
      {% set heading_tag = 'h' ~ (heading_level|default(3)) %}
      {% set title_classes = [
        'event-title',
        'text-red-400',
        'heading-3',
      ] %}
      <{{ heading_tag }} class="{{ title_classes|join(' ') }}">
        {% if url %}
          <a href="{{ url }}" class="no-underline block">
            {{ slots.title }}
          </a>
        {% else %}
          {{ slots.title }}
        {% endif %}
      </{{ heading_tag }}>
    {% endif %}

    {# Event date #}
    {% if slots.date and not past_event %}
      <div class="event-date text-2">
        {{ slots.date }}
      </div>
    {% endif %}

    {# Event place #}
    {% if slots.place %}
      <div class="event-place body-1">
        <span class="font-bold">{{ place_label|default('Place'|t) }}</span>: {{ slots.place }}
      </div>
    {% endif %}

    {# Event address #}
    {% if slots.address %}
      <div class="event-address body-1">
        <span class="font-bold">{{ address_label|default('Address'|t) }}</span>: {{ slots.address }}
      </div>
    {% endif %}

    {% if not slots.place and not slots.address %}
      <div class="event-no-place-address body-1">
        {{ no_place_address_text|default('Address and location to be announced, still being finalized'|t) }}
      </div>
    {% endif %}

    {# Read more link #}
    {% if url %}
      <div class="event-read-more">
        <a href="{{ url }}" class="link-primary flex items-center">
          {{ read_more_text|default('Read more'|t) }}
          <svg class="w-4.5 h-4.5 ml-2" viewBox="0 0 18 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.1313 10.25H3V8.75H12.1313L7.93125 4.55L9 3.5L15 9.5L9 15.5L7.93125 14.45L12.1313 10.25Z"/>
          </svg>
        </a>
      </div>
    {% endif %}
  </div>

  {% if variant == 'featured' %}
    {# Featured layout wrapper #}
    </div>
  {% endif %}
</div>
