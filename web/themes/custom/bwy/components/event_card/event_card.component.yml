$schema: https://git.drupalcode.org/project/drupal/-/raw/10.1.x/core/modules/sdc/src/metadata.schema.json
name: Event card
description: 'Displays an event in a card format with image, title, address and other fields.'
group: Event components
variants:
  default:
    title: Default
  featured:
    title: Featured
slots:
  image:
    title: Image
    description: 'Featured image for the event.'
  title:
    title: Title
    description: 'Event title.'
  date:
    title: Date
    description: 'Event start-end date.'
  place:
    title: Place
    description: 'Event place.'
  address:
    title: Address
    description: 'Event address.'
  read_more_link:
    title: Read more link
    description: 'Link to the full event page.'
props:
  type: object
  properties:
    variant:
      title: 'Variant'
      type: string
      enum:
        - default
        - featured
      'meta:enum':
        default: Default
        featured: Featured
      default: default
    heading_level:
      title: 'Heading level'
      type: integer
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
      'meta:enum':
        1: h1
        2: h2
        3: 'h3 (Default)'
        4: h4
        5: h5
        6: h6
      default: 3
    url:
      title: 'Event URL'
      type: string
      description: 'URL to the full event page.'
    read_more_text:
      title: 'Read more text'
      type: string
      description: 'Text for the read more link. If not provided, uses translatable "Read more" text.'
    category_text:
      title: 'Category text'
      type: string
      description: 'Category badge text for featured variant. Only displayed when variant is "featured".'
    no_place_address_text:
      title: 'No place and address text'
      type: string
      description: 'Text to display when there is no place and address. Only displayed when there is no place and address.'
    place_label:
      title: 'Place label'
      type: string
      description: 'Label for the place. Only displayed when there is a place.'
    address_label:
      title: 'Address label'
      type: string
      description: 'Label for the address. Only displayed when there is an address.'
    current_event:
      title: 'Current event'
      type: boolean
      description: 'Whether the event is currently ongoing.'
    past_event:
      title: 'Past event'
      type: boolean
      description: 'Whether the event has already ended.'
