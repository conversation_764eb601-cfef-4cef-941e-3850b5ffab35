# Hero Section Component

A hero section component that displays a background image with breadcrumbs and title by default. Can include an optional subtitle and CTA button.

## Properties

- `variant` (string, optional): The background image variant to use. Available options:

  - `default`
  - `blue`
  - `red`
  - `green`
  - `pink`
  - `yellow`

- `is_breadcrumbs` (boolean, optional): Whether to display the breadcrumbs block. Defaults to true.
- `is_title` (boolean, optional): Whether to display the page title. Defaults to true.
- `title` (string, optional): Custom title text. If not provided, uses node.label.
- `subtitle` (string, optional): Additional text displayed below the main title. Will not be rendered if empty.
- `cta` (object, optional): Configuration for the Call to Action button
  - `title` (string, required): The text to display on the button
  - `url` (string, required): The URL where the button should link to

## Example Usage

```twig
{# Default usage - shows breadcrumbs and title #}
{% include '@bwy/hero_section/hero_section.twig' %}

{# With subtitle #}
{% include '@bwy/hero_section/hero_section.twig' with {
  title: 'some title',
  subtitle: 'some subtitle',
} %}

{# Without title but with subtitle #}
{% include '@bwy/hero_section/hero_section.twig' with {
  is_title: false,
  subtitle: 'some subtitle',
} %}

{# Complete example with all features #}
{% include '@bwy/hero_section/hero_section.twig' with {
  variant: 'blue',
  title: 'some title',
  subtitle: 'some subtitle',
  cta: {
    title: 'link text',
    url: '/link-url'
  }
} %}

{# Only subtitle and CTA #}
{% include '@bwy/hero_section/hero_section.twig' with {
  is_breadcrumbs: false,
  is_title: false,
  subtitle: 'some subtitle',
  cta: {
    title: 'link text',
    url: '/link-url'
  }
} %}
```
