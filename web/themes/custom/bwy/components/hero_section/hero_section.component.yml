name: Hero Section
description: A hero section component with configurable background image, breadcrumbs, title, subtitle and CTA button
props:
  type: object
  properties:
    variant:
      type: string
      enum: [default, blue, red, green, pink, yellow]
      default: default
    is_breadcrumbs:
      type: boolean
      default: true
    is_title:
      type: boolean
      default: true
    title:
      type: string
      default: ''
    subtitle:
      type: string
      description: Optional subtitle text to display below the main title
      default: ''
    cta:
      type: object
      properties:
        title:
          type: string
          description: The text to display on the CTA button
        url:
          type: string
          description: The URL where the CTA button should link to
      required: [title, url]
