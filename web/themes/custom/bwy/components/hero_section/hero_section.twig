{# Hero Section Component #}
{# Set default values for visibility #}
{% set show_breadcrumbs = is_breadcrumbs is not defined or is_breadcrumbs %}
{% set show_title = is_title is not defined or is_title %}
{% set is_no_content = not show_title and not subtitle and not (cta.title is defined and cta.url is defined) %}
{% set special_class = is_no_content ? 'md:bg-section-header bg-transparent md:text-background-main text-text-main py-6 pt-6 pb-5 md:py-0 md:pt-67 md:pb-60px' : 'bg-section-header text-background-main pt-124 pb-34 md:pt-67 md:pb-60px' %}

{% set classes = [
  'hero',
  variant ? 'hero-' ~ variant : 'hero-default',
  'min-h-380',
  'relative',
  'overflow-hidden',
  special_class
] %}

<div {{ attributes.addClass(classes) }}>
  <div class="absolute -top-1/12 -right-1/12 md:top-0 md:right-0 overflow-hidden">
    <img class="background {{ is_no_content ? 'tw-hidden md:block' : '' }}" src="{{ file_url(directory ~ '/components/hero_section/images/' ~ (variant ? variant : 'default') ~ '.png') }}" alt="">
  </div>

  <div class="hero--content-wrapper bwy-container relative z-10">
    {% if show_breadcrumbs %}
      <div class="mb-6">
        {{ drupal_block('system_breadcrumb_block', {label: false}) }}
      </div>
    {% endif %}
    {% if not is_no_content %}
      <div class="main-content">
        <div class="w-full md:w-content-7">
          {% if show_title or (subtitle is defined and subtitle) %} 
            <div class="flex flex-col gap-1 md:gap-2">
              {% if show_title %}
                {% include 'bwy:heading' with {
                  heading_html_tag: 'h1',
                  content: title ? title : node.label,
                } %}
              {% endif %}

              {% if subtitle is defined and subtitle %}
                {% include 'bwy:heading' with {
                heading_html_tag: 'h2',
                content: subtitle,
              } %}
              {% endif %}
            </div>
          {% endif %}

          {% if cta.title is defined and cta.url is defined %}
            <div class="mt-6">
              {% include '@bwy/components/button/button.twig' with {
                tag: 'a',
                text: cta.title,
                button_type: 'primary',
                button_attributes: create_attribute().setAttribute('href', cta.url)
              } %}
            </div>
          {% endif %}
        </div>
      </div>
    {% endif %}
  </div>
</div>
