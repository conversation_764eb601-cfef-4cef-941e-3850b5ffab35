{#
/**
 * @file
 * Theme override for a breadcrumb trail.
 *
 * Available variables:
 * - breadcrumb: Breadcrumb trail items.
 * - is_front: Boolean indicating if current page is the front page.
 */
#}

{% if breadcrumb %}
  {% set items = [] %}
  {% for item in breadcrumb %}
    {% if item.url %}
      {% set items = items|merge([{
        text: item.text,
        url: item.url
      }]) %}
    {% endif %}
  {% endfor %}

  {% if items is not empty %}
    {% include '@bwy/components/breadcrumb/breadcrumb.twig' with {
      items: items,
      is_front: is_front,
      breadcrumb: breadcrumb
    } only %}
  {% endif %}
{% endif %}
