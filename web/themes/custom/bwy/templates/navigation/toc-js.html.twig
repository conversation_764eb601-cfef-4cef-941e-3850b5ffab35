{#
/**
 * @file
 * Default theme implementation to display a responsive table of contents.
 *
 * Returns HTML for a responsive table of contents.
 *
 * Available variables:
 * - entity: the entity the TOC belongs to.
 * - title: the title to display for the table of content.
 * - tag: the tag to use for the title.
 *
 * @ingroup themeable
 */
#}
{% include '@bwy/components/toc/toc.twig' with {
  title: title,
  tag: tag,
  title_attributes: title_attributes,
  attributes: attributes
} %}
