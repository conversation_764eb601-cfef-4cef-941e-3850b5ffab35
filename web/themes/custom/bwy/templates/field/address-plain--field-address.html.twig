{#
/**
 * @file
 * Default template for the 'plain' address formatter.
 *
 * Available variables:
 *   - given_name: Given name.
 *   - additional_name: Additional name.
 *   - family_name: Family name.
 *   - organization: Organization.
 *   - address_line1: First address line.
 *   - address_line2: Second address line.
 *   - address_line3: Third address line.
 *   - postal_code: Postal code.
 *   - sorting_code: Sorting code.
 *   - dependent_locality: The dependent locality.
 *     - dependent_locality.code: Dependent locality code.
 *     - dependent_locality.name: Dependent locality name.
 *   - locality: The locality subdivision.
 *     - locality.code: Locality code.
 *     - locality.name: Locality name.
 *   - administrative_area: The administrative area subdivision.
 *     - administrative_area.code: Administrative area code.
 *     - administrative_area.name: Administrative area name.
 *   - country: The country.
 *     - country.code: Country code.
 *     - country.name: Country name.
 *   - address: An object that implements \Drupal\address\AddressInterface
 *     representing the address to be rendered. Only public methods can be used.
 *   - view_mode: View mode of the entity being rendered that this address field
 *     is attached to. For example, "teaser" or "full".
 *
 * if a subdivision (dependent_locality, locality, administrative_area) was
 * entered, the array will always have a code. If it's a predefined subdivision,
 * it will also have a name. The code is always preferred.
 *
 * @ingroup themeable
 */
#}
{% set address_parts = [] %}

{# Collect name parts #}
{% if given_name or family_name %}
  {% set address_parts = address_parts|merge([given_name ~ ' ' ~ family_name]) %}
{% endif %}

{# Add organization #}
{% if organization %}
  {% set address_parts = address_parts|merge([organization]) %}
{% endif %}

{# Add address lines #}
{% if address_line1 %}
  {% set address_parts = address_parts|merge([address_line1]) %}
{% endif %}
{% if address_line2 %}
  {% set address_parts = address_parts|merge([address_line2]) %}
{% endif %}
{% if address_line3 %}
  {% set address_parts = address_parts|merge([address_line3]) %}
{% endif %}

{# Add dependent locality #}
{% if dependent_locality.code %}
  {% set address_parts = address_parts|merge([dependent_locality.code]) %}
{% endif %}

{# Add locality, postal code and administrative area #}
{% if locality.code or postal_code or administrative_area.code %}
  {% set location_part = [locality.code, postal_code, administrative_area.code]|filter(v => v is not empty)|join(' ') %}
  {% if location_part is not empty %}
    {% set address_parts = address_parts|merge([location_part]) %}
  {% endif %}
{% endif %}

{# Add country #}
{% if country.name %}
  {% set address_parts = address_parts|merge([country.name]) %}
{% endif %}

{# Join all parts with commas #}
{% set address_text = address_parts|join(', ') %}

{# Render using icon_with_text component #}
{% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
  icon_name: 'location',
  text: address_text
} %}
