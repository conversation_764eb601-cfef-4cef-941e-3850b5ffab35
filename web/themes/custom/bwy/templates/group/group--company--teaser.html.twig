{#
/**
 * @file
 * Default theme implementation to display a group.
 *
 * Available variables:
 * - group: The group entity with limited access to object properties and
 *   methods. Only "getter" methods (method names starting with "get", "has",
 *   or "is") and a few common methods such as "id" and "label" are available.
 *   Calling other methods (such as group.delete) will result in an exception.
 * - label: The title of the group.
 * - content: All group items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the
 *   printing of a given child element.
 * - url: Direct URL of the current group.
 * - attributes: HTML attributes for the containing element.
 *   The attributes.class element may contain one or more of the following
 *   classes:
 *   - group: The current template type (also known as a "theming hook").
 *   - group--[type]: The current group type. For example, if the group is a
 *     "Classroom" it would result in "group--classroom". Note that the machine
 *     name will often be in a short form of the human readable label.
 *   - group--[view_mode]: The View Mode of the group; for example, a
 *     teaser would result in: "group--teaser", and full: "group--full".
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - content_attributes: Same as attributes, except applied to the main
 *   content tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - page: Flag for the full page state. Will be true if view_mode is 'full'.
 *
 * @see template_preprocess_group()
 *
 * @ingroup themeable
 */
#}
<div{{attributes}}>
  {{ title_prefix }}
  {{ title_suffix }}
  {% include '@bwy/components/company_header/company_header.twig' with {
    company_logo: content.field_logo,
    is_top_company: content.field_top_employer,
    company_name: label,
    company_description: content.field_description,
    company_address: content.field_address,
    company_website: content.field_website,
    company_social_media: content.field_social_media,
  } %}
</div>
