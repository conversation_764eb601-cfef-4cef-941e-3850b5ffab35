{#
/**
 * @file
 * Default theme implementation to display primary and secondary local tasks.
 *
 * Available variables:
 * - primary: HTML list items representing primary tasks.
 * - secondary: HTML list items representing secondary tasks.
 *
 * Each item in these variables (primary and secondary) can be individually
 * themed in menu-local-task.html.twig.
 *
 * @ingroup themeable
 */
#}
{% if primary %}
  <ul
    class="flex border-b border-border-main gap-1 text-sm font-medium">
    {{ primary }}
  </ul>
{% endif %}
{% if secondary %}
  <ul
class="flex border-b border-gray-300 space-x-4 text-sm font-size-button mt-4">

    {{ secondary }}
  </ul>
{% endif %}


