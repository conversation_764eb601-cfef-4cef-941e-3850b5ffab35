{#
/**
 * @file
 * Default theme implementation for a local task link.
 *
 * Available variables:
 * - attributes: HTML attributes for the wrapper element.
 * - is_active: Whether the task item is an active tab.
 * - link: A rendered link element.
 *
 * Note: This template renders the content for each task item in
 * menu-local-tasks.html.twig.
 *
 * @see template_preprocess_menu_local_task()
 *
 * @ingroup themeable
 */
#}
<li
  class="relative">
  {% set link_text = link['#title'] %}
  {% set link_url = link['#url'] is defined ? link['#url'].toString() : '#' %}
  {% set link_classes = [
    'inline-block',
    'py-2',
    'px-4',
    'text-center',
    'border',
    'transition-all',
    'duration-200',
    'link-text',
    is_active
      ? 'border-[var(--color-blue-600)] text-[var(--color-blue-600)] font-semibold'
      : 'border-transparent text-color-text-secondary hover:border-[var(--color-border-main)] hover:text-[var(--color-text-main)]'
  ] %}
  <a
    href="{{ link_url }}" class="{{ link_classes|join(' ') }}">
    {{ link_text }}
  </a>
</li>






