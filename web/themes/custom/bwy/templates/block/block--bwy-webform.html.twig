{#
/**
 * @file
 * Theme override for a webform block.
 *
 * Available variables:
 * - plugin_id: The ID of the block implementation.
 * - label: The configured label of the block if visible.
 * - configuration: A list of the block's configuration values.
 * - content: The content of this block.
 * - attributes: array of HTML attributes populated by modules.
 * - label_hidden: A boolean, used to hide the label if configured to do so.
 * - title_attributes: HTML attributes for the title.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 */
#}
{%
  set classes = [
    'block',
    'block-webform',
    'block-' ~ configuration.provider|clean_class,
    'block-' ~ plugin_id|clean_class,
    'bwy-container',
    'mb-10',
    'p-8',
    'bg-background-main/75',
    'border',
    'border-solid',
    'border-border-main',
    'rounded-lg',
  ]
%}

<div{{ attributes.addClass(classes) }}>
  {{ title_prefix }}
  {% if label and not label_hidden %}
    <h2{{ title_attributes.addClass('heading-3 mb-9') }}>{{ label }}</h2>
  {% endif %}
  {{ title_suffix }}
  {% block content %}
    <div class="block-webform__content">
      {{ content }}
    </div>
  {% endblock %}
</div>
