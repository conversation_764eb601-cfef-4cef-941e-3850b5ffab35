{#
/**
 * @file
 * Theme override to display a event node in teaser view mode.
 *
 * This template uses the event_card SDC component to render events
 * in a consistent card format.
 *
 * Available variables:
 * - node: The node entity with limited access to object properties and methods.
 * - label: The title of the node.
 * - content: All node items.
 * - url: Direct URL of the current node.
 * - date: Themed creation date field.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - attributes: HTML attributes for the containing element.
 *
 * @see template_preprocess_node()
 */
#}

{# Include the event card component #}
{% set past_event = node.field_date.end_value < "now"|date('U') %}
{% set current_event = node.field_date.value < "now"|date('U') and node.field_date.end_value >= "now"|date('U') %}

{%
  include 'bwy:event_card' with {
    variant: 'default',
    slots: {
      image: content.field_teaser_image[0],
      title: node.field_listing_title.value | default(node.label),
      date: content.event_date,
      location: node.field_location.value[0].locality ~ ', ' ~ content.country_name,
      place: node.field_place.value,
      address: node.field_location.value[0].address_line1,
    },
    url: url,
    heading_level: 3,
    place_label: content.field_place['#title'],
    address_label: content.field_location['#title'],
    current_event: current_event,
    past_event: past_event,
  } only
%}
