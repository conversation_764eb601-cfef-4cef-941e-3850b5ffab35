<?php

/**
 * @file
 * Paragraph preprocess functions.
 */

use <PERSON><PERSON>al\views\Views;

/**
 * Implements hook_preprocess_paragraph().
 */
function bwy_preprocess_paragraph__schedule_slot(&$variables) {
  /** @var \Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['paragraph'];

  // Get the referenced speaker entities.
  $variables['content']['speakers'] = [];
  if ($paragraph->hasField('field_nodes') && !$paragraph->get('field_nodes')->isEmpty()) {
    $variables['content']['speakers'] = $paragraph->get('field_nodes')->referencedEntities();
  }
}

/**
 * Implements hook_preprocess_paragraph().
 */
function bwy_preprocess_paragraph(&$variables) {
  if ($variables['paragraph']->bundle() === 'employers') {
    $view = Views::getView('employers');
    if ($view) {
      $view->setDisplay('employers');
      $view->preExecute();
      $view->execute();
      $variables['employers'] = $view->render();
    }
  }
}
