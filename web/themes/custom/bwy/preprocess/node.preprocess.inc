<?php

/**
 * @file
 * Node preprocess functions.
 */

use Drupal\block\Entity\Block;
use <PERSON>upal\Core\Datetime\DrupalDateTime;
use Drupal\Core\Language\LanguageInterface;

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node__news(&$variables) {
  if (in_array($variables['view_mode'], ['teaser', 'featured', 'full']) && !empty($variables['content']['field_image_media'][0])) {
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'w-full';
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'h-full';
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'object-cover';
  }

  // Programmatically load and render the breadcrumb block for use in templates.
  $block = Block::load('system_breadcrumb_block');
  if ($block) {
    $view_builder = \Drupal::entityTypeManager()->getViewBuilder('block');
    $variables['breadcrumb_block'] = $view_builder->view($block);
  }
}

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node__event(&$variables) {
  /** @var \Drupal\node\Entity\Node $node */
  $node = $variables['node'];

  // Ensure 'field_date' and 'field_date_show_month_only'
  // fields exist and are not empty.
  if ($node->hasField('field_date') && !$node->get('field_date')->isEmpty() &&
      $node->hasField('field_date_show_month_only') &&
      !$node->get('field_date_show_month_only')->isEmpty()) {

    $show_month_only = (bool) $node->get('field_date_show_month_only')->value;

    $start_date_iso = $node->get('field_date')->value;
    $end_date_iso = $node->get('field_date')->end_value;

    try {
      $start_date_obj = new DrupalDateTime($start_date_iso);
      $end_date_obj = new DrupalDateTime($end_date_iso);
    }
    catch (\Exception $e) {
      // Handle invalid date strings.
      $variables['content']['event_date'] = ['#markup' => 'Invalid Date'];
      \Drupal::logger('bwy_theme')
        ->error(
          'Failed to parse date range for node @nid: @message',
          ['@nid' => $node->id(), '@message' => $e->getMessage()]
        );
      return;
    }

    // Initialize the string.
    $formatted_date_string = '';
    $date_formatter = \Drupal::service('date.formatter');

    if ($show_month_only) {
      // If the checkbox is checked, display only the month of the start date.
      $formatted_date_string = $date_formatter->format($start_date_obj->getTimestamp(), 'bwy_event_date_month_only');
    }
    else {
      $start_day = $start_date_obj->format('j');
      $end_day = $end_date_obj->format('j');
      $start_month = $start_date_obj->format('n');
      $end_month = $end_date_obj->format('n');
      $start_year = $start_date_obj->format('Y');
      $end_year = $end_date_obj->format('Y');

      if ($start_date_obj->format('Y-m-d') === $end_date_obj->format('Y-m-d')) {
        // Case 1: Start date and end date are on the exact same day.
        $formatted_date_string = $date_formatter->format($start_date_obj->getTimestamp(), 'bwy_event_date');
      }
      elseif ($start_year === $end_year && $start_month === $end_month) {
        // Case 2: Different days, but within the same month and year.
        $formatted_date_string = $start_day . '-' . $end_day . ' ' . $date_formatter->format($start_date_obj->getTimestamp(), 'custom', 'F Y');
      }
      elseif ($start_year === $end_year) {
        // Case 3: Different months, but within the same year.
        $formatted_start = $date_formatter->format($start_date_obj->getTimestamp(), 'custom', 'j F');
        $formatted_end = $date_formatter->format($end_date_obj->getTimestamp(), 'custom', 'j F Y');
        $formatted_date_string = $formatted_start . ' - ' . $formatted_end;
      }
      else {
        // Case 4: Dates span across different years.
        $formatted_start = $date_formatter->format($start_date_obj->getTimestamp(), 'custom', 'j F Y');
        $formatted_end = $date_formatter->format($end_date_obj->getTimestamp(), 'custom', 'j F Y');
        $formatted_date_string = $formatted_start . ' - ' . $formatted_end;
      }
    }

    // Assign the formatted string directly to $variables['content']['event_date']
    // wrapped in a #markup property to make it renderable by Drupal.
    $variables['content']['event_date'] = ['#markup' => $formatted_date_string];
  }
  else {
    // If the fields are missing or empty, ensure the variable is set to an empty render array.
    $variables['content']['event_date'] = ['#markup' => ''];
  }

  $address = $node->get('field_location')->first();
  if ($address) {
    $country_code = $address->getCountryCode();
    $langcode = \Drupal::languageManager()->getCurrentLanguage(LanguageInterface::TYPE_INTERFACE)->getId();
    $country = \Drupal::service('address.country_repository')->get($country_code, $langcode);
    $variables['content']['country_name'] = $country ? $country->getName() : '';
  }

  if (in_array($variables['view_mode'], ['teaser', 'featured', 'full']) &&
      !empty($variables['content']['field_teaser_image'][0])) {
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'w-full';
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'h-full';
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'object-cover';
  }

  // Get the referenced speaker entities.
  $variables['content']['speakers'] = [];
  if ($node->hasField('field_schedule') && !$node->get('field_schedule')->isEmpty()) {
    /** @var \Drupal\paragraphs\Entity\Paragraph $paragraph */
    $schedule = $node->get('field_schedule')->entity;

    // Get the referenced speaker entities.
    $variables['content']['speakers'] = [];
    if ($schedule->hasField('field_nodes') && !$schedule->get('field_nodes')->isEmpty()) {
      $variables['content']['speakers'] = $schedule->get('field_nodes')->referencedEntities();
    }
  }
}

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node(&$variables) {
  /** @var \Drupal\node\Entity\Node $node */
  $node = $variables['node'];

  // Process salary information for job_post content type.
  if ($node->bundle() === 'job_post') {
    if (
      $node->hasField('field_salary_from') &&
      $node->hasField('field_salary_to') &&
      $node->hasField('field_gross_net')
    ) {

      $salary_from = $variables['content']['field_salary_from'][0]['#markup'] ?? '';
      $salary_to = $variables['content']['field_salary_to'][0]['#markup'] ?? '';
      $gross_net = $variables['content']['field_gross_net'][0]['#markup'] ?? '';

      if (!empty($salary_from) && !empty($salary_to) && !empty($gross_net)) {
        $variables['salary_range'] = [
          '#type' => 'salary_range',
          '#markup' => $salary_from . ' - ' . $salary_to . ' (' . $gross_net . ')',
        ];
      }
    }
  }
}
