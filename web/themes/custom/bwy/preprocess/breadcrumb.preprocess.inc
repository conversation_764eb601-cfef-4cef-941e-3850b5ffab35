<?php

/**
 * @file
 * Breadcrumb preprocess functions.
 */

/**
 * Implements hook_preprocess_breadcrumb().
 */
function bwy_preprocess_breadcrumb(&$variables) {
  $request = \Drupal::request();
  $route_match = \Drupal::service('current_route_match');
  $title = \Drupal::service('title_resolver')->getTitle($request, $route_match->getRouteObject());

  if ($title) {
    // Add the current page title as the last breadcrumb item.
    $variables['breadcrumb'][] = [
      'text' => $title,
      'url' => NULL,
    ];
  }
}
