<?php

namespace Drupal\bwy_calculator\Form;

use Drupal\bwy_calculator\Plugin\Field\FieldType\BWYCalculator;
use <PERSON>upal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 *
 */
class CalculatorDataForm extends FormBase {

  /**
   * @var \Drupal\Core\Entity\EntityStorageInterface
   */
  protected $storage;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    $instance = new static();
    $instance->storage = $container->get('entity_type.manager')->getStorage("node");
    return $instance;
  }

  /**
   *
   */
  public function getFormId() {
    return "bwy_calculator_data";
  }

  /**
   *
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    /** @var \Drupal\node\NodeInterface[] $nodes */
    $nodes = $this->storage->loadByProperties(['type' => 'city']);

    $form['table'] = [
      '#type' => 'table',
      '#tree' => TRUE,
      '#sticky' => TRUE,
      '#responsive' => TRUE,
      '#header' => [
        'city' => $this->t("City"),
      ],
    ];

    $expenses = BWYCalculator::getExpenses();

    foreach ($expenses as $key => $label) {
      $form['table']['#header'][$key] = $label;
    }

    foreach ($nodes as $node) {
      $row = [
        'city' => [
          '#type' => 'markup',
          '#markup' => $node->label(),
        ],
      ];
      $first = $node->get('field_calculator_data')->first();
      $data = $first ? $first->getValue() : [];

      foreach ($expenses as $key => $label) {
        $row[$key] = [
          '#type' => "number",
          '#default_value' => $data[$key] ?? 0.00,
        ];
      }
      $form['table'][$node->id()] = $row;
    }

    return $form;
  }

  /**
   *
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // @todo Implement submitForm() method.
  }

}
