<?php

namespace Drupal\bwy_calculator\Plugin\WebformHandler;

use <PERSON><PERSON>al\Core\Render\Markup;
use <PERSON><PERSON><PERSON>\webform\Plugin\WebformHandlerBase;
use <PERSON><PERSON>al\webform\WebformSubmissionInterface;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;

/**
 * Form submission handler.
 *
 * @WebformHandler(
 *   id = "bwy_calculator",
 *   label = @Translation("BWY Calculator"),
 *   category = @Translation("BWY"),
 *   description = @Translation("Caclulate the result."),
 *   cardinality = \Drupal\webform\Plugin\WebformHandlerInterface::CARDINALITY_SINGLE,
 *   results = \Drupal\webform\Plugin\WebformHandlerInterface::RESULTS_PROCESSED,
 * )
 */
class <PERSON><PERSON><PERSON>andler extends WebformHandlerBase {

  /**
   * @var \Drupal\Core\Entity\EntityStorageInterface
   */
  protected $storage;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $instance = parent::create($container, $configuration, $plugin_id, $plugin_definition);
    $instance->storage = $container->get("entity_type.manager")->getStorage("node");
    return $instance;
  }

  /**
   * {@inheritdoc}
   */
  public function preSave(WebformSubmissionInterface $webform_submission) {
    $values = $webform_submission->getData();
    /** @var \Drupal\Core\Entity\ContentEntityInterface $city */
    $city = $this->storage->load($values['target_city']);
    $data = $city->get("calculator_data")->first()->getValue();

    $result = [];

    // Kids expenditures.
    if (intval($values['kids']) !== 0) {
      // The number of kids is multiplied by the montly kindergarten tax.
      $result['kindergarten'] = intval($values['kids']) * $data['kindergarten'];
    }
    // Transportation expenditures.
    if ($values['transport'] === 'biking-walking') {
      $result['transportation'] = $data['biking_walking'];
    }
    elseif ($values['transport'] === 'public-transport') {
      $result['transportation'] = $data['public_transport'];
    }
    elseif ($values['transport'] === 'fuel') {
      // Days.
      $workingDaysPerMonth = 22;
      // Kilometers.
      $avrageDailyDistanece = 50;
      // Liters per 100 kilometers.
      $carFuelConsumption       = 0.06;
      $result['transportation'] = $data['fuel'] * $workingDaysPerMonth * $avrageDailyDistanece * $carFuelConsumption;
    }

    // Entertainment expenditures.
    if (floatval($values['entertainment']) > 0) {
      // The number of times a person goes out is multiplied by the price for going out.
      $result['entertainment'] = floatval($values['entertainment']) * $data['going_out'];
    }

    // Eating-out expenditures.
    if (floatval($values['eating_out']) > 0) {
      // The number of times a person eats-out is multiplied by the price for a restarunt.
      $result['eating-out'] = floatval($values['eating_out']) * $data['restaurant'];
    }

    // Eating at home expenditures.
    $dayPerMonth              = 30;
    $dayEatingAtHome          = intval($dayPerMonth - floatval($values['eating_out']));
    $result['eating-at-home'] = $dayEatingAtHome * $data['food'];

    // Housing expenditures.
    if ($values['housing'] === 'rent') {
      $result['housing'] = $data['rent'];
    }
    elseif ($values['housing'] === 'own-house') {
      $result['housing'] = $data['own_house'];
    }
    elseif ($values['housing'] === 'mortgage') {
      $result['housing'] = $data['mortgage'];
    }

    // Utilites expenditures.
    $result['utilities'] = $data['utilities'];

    $result['total'] = array_sum($result);

    $result['current_expenses'] = $values['current_expenses'] ?? 0;

    if ($values['currency'] === 'eur') {
      $result['current_expenses'] /= 0.51129;
    }
    elseif ($values['currency'] === 'usd') {
      $result['current_expenses'] /= 0.53760;
    }

    $result['total_eur'] = $result['total'] * 0.51129;
    $result['total_usd'] = $result['total'] * 0.53760;

    $result['current_expenses_eur'] = $result['current_expenses'] * 0.51129;
    $result['current_expenses_usd'] = $result['current_expenses'] * 0.53760;

    foreach ($result as &$item) {
      $item = ceil($item);
    }

    if ($result['current_expenses'] > 0) {
      $result['diff'] = 100 - round(($result['total'] / $result['current_expenses']) * 100, 1);
    }

    $build = [
      '#type' => "table",
      "#header" => ['#', "Разход", "Сума (лв.)"],
      '#rows' => [
        ["1", "Детска градина/училище", "0 лв"],
        ["2", "Транспорт до работа", $result['transportation'] . " лв"],
        ["3", "Забавление навън", $result['entertainment'] . " лв"],
        ["4", "Хранене навън", $result['eating-out'] . " лв"],
        ["5", "Хранене в къщи", $result['eating-at-home'] . " лв"],
        ["6", "Жилище", $result['housing'] . " лв"],
        ["7", "Комунални услуги", $result['utilities'] . " лв"],
        [
          "",
          Markup::create("<strong>За да живееш в " . $city->label() . ", ще ти трябват ОБЩО:</strong>"),
          Markup::create("<strong>" . $result['total'] . " лв</strong>"),
        ],
        [
          "",
          "",
          Markup::create("<strong>€ " . $result['total_eur'] . "</strong>"),
        ],
        [
          "",
          "",
          Markup::create("<strong>$ " . $result['total_usd'] . "</strong>"),
        ],
      ],
    ];

    $values['results'] = json_encode($result);
    $values['table'] = ['value' => \Drupal::service('renderer')->render($build), 'format' => 'full_html'];
    $webform_submission->setData($values);
  }

}
