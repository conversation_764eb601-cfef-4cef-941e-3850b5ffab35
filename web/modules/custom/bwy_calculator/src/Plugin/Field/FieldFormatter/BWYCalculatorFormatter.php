<?php

namespace Drupal\bwy_calculator\Plugin\Field\FieldFormatter;

use <PERSON><PERSON>al\bwy_calculator\Plugin\Field\FieldType\BWYCalculator;
use <PERSON>upal\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\FormatterBase;

/**
 * Plugin implementation of the 'BWY Calculator data' formatter.
 *
 * @FieldFormatter(
 *   id = "bwy_calculator_data",
 *   label = @Translation("Default"),
 *   field_types = {
 *     "bwy_calculator_data"
 *   }
 * )
 */
class BWYCalculatorFormatter extends FormatterBase {

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    $expenses = BWYCalculator::getExpenses();
    $element = [];

    foreach ($items as $delta => $item) {
      $rows = [];
      foreach ($expenses as $key => $label) {
        $rows[] = [$label, $item->{$key}];
      }
      $element[$delta] = [
        '#theme' => "table",
        '#rows' => $rows,
      ];

    }

    return $element;
  }

}
