<?php

namespace Drupal\bwy_calculator\Plugin\Field\FieldWidget;

use Drupal\bwy_calculator\Plugin\Field\FieldType\BWYCalculator;
use Drupal\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\WidgetBase;
use Drupal\Core\Form\FormStateInterface;

/**
 * A widget.
 *
 * @FieldWidget(
 *   id = "bwy_calculator_data",
 *   label = @Translation("Default"),
 *   field_types = {
 *     "bwy_calculator_data"
 *   }
 * )
 */
class BWYCalculatorWidget extends WidgetBase {

  /**
   * {@inheritdoc}
   */
  private function getGroups() {
    return [
      'kids' => t('Do you have kids?'),
      'transportation' => t('Do you have kids?'),
      'entertainment' => t('How ofter do you go out?'),
      'eating_out' => t('How often do you eating out?'),
      'housing' => t('Do you live in own house?'),
      'fixed' => t('Fixed monthly expenses'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  private function getExpensesGroups() {
    return [
      'kindergarten' => 'kids',
      'public_transport' => 'transportation',
      'fuel' => 'transportation',
      'biking_walking' => 'transportation',
      'going_out' => 'entertainment',
      'restaurant' => 'eating_out',
      'rent' => 'housing',
      'own_house' => 'housing',
      'mortgage' => 'housing',
      'food' => 'fixed',
      'utilities' => 'fixed',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function formElement(FieldItemListInterface $items, $delta, array $element, array &$form, FormStateInterface $form_state) {
    $groups = $this->getGroups();
    $expenses_groups = $this->getExpensesGroups();

    $expenses = BWYCalculator::getExpenses();
    $element += [
      '#type' => 'details',
    ];

    foreach ($groups as $key => $label) {
      $element[$key] = [
        '#type' => 'fieldset',
        '#title' => $label,
      ];
    }

    foreach ($expenses as $key => $label) {
      $element[$expenses_groups[$key]][$key] = [
        '#type' => "number",
        '#title' => $label,
        '#required' => $element['#required'],
        '#min' => "0.00",
        '#step' => "0.01",
        '#default_value' => $items[$delta]->{$key} ?? 0.00,
      ];
    }

    return $element;
  }

  /**
   * {@inheritdoc}
   */
  public function massageFormValues(array $values, array $form, FormStateInterface $form_state) {
    $groups = $this->getExpensesGroups();
    foreach ($values as &$value) {
      if ($value && is_array($value)) {
        foreach ($groups as $expense => $group) {
          if (isset($value[$group]) && isset($value[$group][$expense])) {
            $value[$expense] = (float) $value[$group][$expense];
            unset($value[$group][$expense]);
          }
        }
      }
    }
    return parent::massageFormValues($values, $form, $form_state);
  }

}
