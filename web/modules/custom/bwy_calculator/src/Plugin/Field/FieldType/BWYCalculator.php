<?php

namespace Drupal\bwy_calculator\Plugin\Field\FieldType;

use <PERSON>upal\Core\Field\FieldItemBase;
use <PERSON>upal\Core\Field\FieldStorageDefinitionInterface;
use Drupal\Core\TypedData\DataDefinition;

/**
 * Provides a field for calculator data.
 *
 * @FieldType(
 *   id = "bwy_calculator_data",
 *   label = @Translation("Calculator Data"),
 *   default_formatter = "bwy_calculator_data",
 *   default_widget = "bwy_calculator_data",
 * )
 */
class BWYCalculator extends FieldItemBase {

  /**
   * Get the expenses.
   */
  public static function getExpenses() {
    return [
      'kindergarten' => t('Monthly kindergarten fee'),
      'public_transport' => t('Monthly cost of a public transport pass'),
      'fuel' => t('Price per liter of fuel (petrol)'),
      'biking_walking' => t('Bicycle/walking'),
      'going_out' => t('Cost of per going out'),
      'restaurant' => t('Cost per eating out'),
      'rent' => t('Monthly housing rent (downtown)'),
      'own_house' => t('Own house'),
      'mortgage' => t('Monthly mortgage payment'),
      'food' => t('Daily grocery expenses'),
      'utilities' => t('Monthly cost of utilities'),
    ];
  }

  /**
   * {@inheritdoc}
   */
  public static function propertyDefinitions(FieldStorageDefinitionInterface $field_definition) {
    $properties = [];

    $keys = self::getExpenses();

    foreach ($keys as $key => $label) {
      $properties[$key] = DataDefinition::create('float')
        ->setLabel($label)
        ->setRequired(TRUE);
    }

    return $properties;
  }

  /**
   * {@inheritdoc}
   */
  public static function schema(FieldStorageDefinitionInterface $field_definition) {
    $columns = [];

    $keys = array_keys(self::getExpenses());

    foreach ($keys as $key) {
      $columns[$key] = [
        'type' => 'float',
        'not null' => TRUE,
        'default' => 0.00,
      ];
    }

    return [
      'columns' => $columns,
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function isEmpty() {
    $keys = array_keys(self::getExpenses());

    foreach ($keys as $key) {
      $value = $this->get($key)->getValue();
      if ($value === NULL || $value === '') {
        return TRUE;
      }
    }

    return FALSE;
  }

}
