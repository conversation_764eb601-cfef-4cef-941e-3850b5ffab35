<?php

namespace Drupal\bwy_general\Plugin\Block;

use <PERSON>upal\Core\Block\BlockBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Provides a 'Footer contact' block.
 *
 * @Block(
 *   id = "bwy_footer_contact_block",
 *   admin_label = @Translation("Footer contact block"),
 *   category = @Translation("BWY")
 * )
 */
class FooterContactBlock extends BlockBase {

  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return [
      'address' => '1164 Sofia, Bulgaria, 22 Galichitsa Str.',
      'phone' => '+359877496777',
      'email' => '<EMAIL>',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function blockForm($form, FormStateInterface $form_state) {
    $form['address'] = [
      '#type' => 'textarea',
      '#title' => $this->t('Address'),
      '#default_value' => $this->configuration['address'],
    ];

    $form['phone'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Phone number'),
      '#default_value' => $this->configuration['phone'],
    ];

    $form['email'] = [
      '#type' => 'email',
      '#title' => $this->t('Email'),
      '#default_value' => $this->configuration['email'],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
    $this->configuration['address'] = $form_state->getValue('address');
    $this->configuration['phone'] = $form_state->getValue('phone');
    $this->configuration['email'] = $form_state->getValue('email');
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    return [
      '#markup' => '
        <div class="flex flex-wrap flex-col gap-4 body-3">
          <div>
            <a rel="noopener noreferrer" target="_blank" href="https://www.google.com/maps/place/' . $this->configuration['address'] . '">' . $this->configuration['address'] . '</a>
          </div>

          <div class="flex flex-wrap flex-col underline break-all">
            <a rel="noopener noreferrer" target="_blank" href="tel:' . $this->configuration['phone'] . '">' . $this->configuration['phone'] . '</a>
            <a rel="noopener noreferrer" target="_blank" href="mailto:' . $this->configuration['email'] . '">' . $this->configuration['email'] . '</a>
          </div>
        </div>
      ',
      '#attributes' => ['class' => ['footer-contact-block']],
    ];
  }

}
