uuid: b69055fd-80d0-4f8f-bb0b-d30d37f99d95
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_education
    - node.type.job_post
    - taxonomy.vocabulary.education
id: node.job_post.field_education
field_name: field_education
entity_type: node
bundle: job_post
label: 'Минимално изискуемо образование'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      education: education
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
