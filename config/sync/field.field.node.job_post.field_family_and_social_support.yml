uuid: 2cd938ac-6060-4cd4-bbed-a657b9e1c802
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_family_and_social_support
    - node.type.job_post
    - taxonomy.vocabulary.family_and_social_support
id: node.job_post.field_family_and_social_support
field_name: field_family_and_social_support
entity_type: node
bundle: job_post
label: 'Семейство и социална подкрепа'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      family_and_social_support: family_and_social_support
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
