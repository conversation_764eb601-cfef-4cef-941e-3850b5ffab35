uuid: 114c4bef-c3cd-4bfa-96d8-1d3e8c007998
langcode: bg
status: true
dependencies:
  config:
    - node.type.landing_page
id: node.landing_page.uid
field_name: uid
entity_type: node
bundle: landing_page
label: 'Authored by'
description: 'The username of the content author.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
