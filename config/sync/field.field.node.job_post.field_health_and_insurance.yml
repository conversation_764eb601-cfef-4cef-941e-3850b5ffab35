uuid: 38621e41-30d1-4beb-9b91-23a558d1fea8
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_health_and_insurance
    - node.type.job_post
    - taxonomy.vocabulary.health_and_insurance
id: node.job_post.field_health_and_insurance
field_name: field_health_and_insurance
entity_type: node
bundle: job_post
label: 'Здраве и осигуряване'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      health_and_insurance: health_and_insurance
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
