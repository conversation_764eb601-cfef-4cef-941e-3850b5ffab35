uuid: 1d53019e-65ef-47b3-978b-bf48ab6b3a4d
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_video_gallery
    - media.type.remote_video
    - node.type.event
id: node.event.field_video_gallery
field_name: field_video_gallery
entity_type: node
bundle: event
label: 'Video gallery'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      remote_video: remote_video
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
