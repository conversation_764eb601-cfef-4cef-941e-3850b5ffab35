uuid: cd2fcbc9-b4d8-48e8-9f05-fb46569a99af
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_gross_net
    - node.type.job_post
  module:
    - options
id: node.job_post.field_gross_net
field_name: field_gross_net
entity_type: node
bundle: job_post
label: 'Бруто / Нето'
description: ''
required: false
translatable: true
default_value:
  -
    value: gross
default_value_callback: ''
settings: {  }
field_type: list_string
