uuid: f3fc80b9-f96c-4952-a030-3f045a277e59
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_region
    - node.type.city
    - taxonomy.vocabulary.city
id: node.city.field_region
field_name: field_region
entity_type: node
bundle: city
label: Region
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      city: city
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
