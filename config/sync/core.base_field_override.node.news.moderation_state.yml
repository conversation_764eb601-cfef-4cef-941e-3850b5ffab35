uuid: e9b80e3e-c24d-49fe-8743-aad0257638cb
langcode: bg
status: true
dependencies:
  config:
    - node.type.news
id: node.news.moderation_state
field_name: moderation_state
entity_type: node
bundle: news
label: 'Moderation state'
description: 'The moderation state of this piece of content.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
