uuid: 3e1c8498-358f-4ac1-8309-e246357e832c
langcode: bg
status: true
dependencies:
  config:
    - node.type.landing_page
id: node.landing_page.created
field_name: created
entity_type: node
bundle: landing_page
label: 'Authored on'
description: 'The date and time that the content was created.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: created
