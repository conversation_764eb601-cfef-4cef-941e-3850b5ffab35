uuid: 4c9df7fe-2efc-4970-bd2c-a824039fe51c
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_formatted_description
    - node.type.event
  module:
    - text
id: node.event.field_formatted_description
field_name: field_formatted_description
entity_type: node
bundle: event
label: Description
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats: {  }
field_type: text_long
