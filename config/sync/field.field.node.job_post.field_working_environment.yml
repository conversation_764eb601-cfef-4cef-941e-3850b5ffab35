uuid: 4f990dcf-b23c-477b-9644-c9f50eeb8795
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_working_environment
    - node.type.job_post
    - taxonomy.vocabulary.working_environment
id: node.job_post.field_working_environment
field_name: field_working_environment
entity_type: node
bundle: job_post
label: 'Работна среда'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      working_environment: working_environment
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
