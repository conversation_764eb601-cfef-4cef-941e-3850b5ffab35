uuid: 42000315-a00c-4c6d-87c5-d0540bb8a467
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.schedule_slot.field_end_time
    - field.field.paragraph.schedule_slot.field_nodes
    - field.field.paragraph.schedule_slot.field_plain_description
    - field.field.paragraph.schedule_slot.field_start_time
    - field.field.paragraph.schedule_slot.field_subtitle
    - field.field.paragraph.schedule_slot.field_title
    - paragraphs.paragraphs_type.schedule_slot
  module:
    - simple_time_field
id: paragraph.schedule_slot.default
targetEntityType: paragraph
bundle: schedule_slot
mode: default
content:
  field_end_time:
    type: simple_time_formatter_three
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_nodes:
    type: entity_reference_label
    label: hidden
    settings:
      link: true
    third_party_settings: {  }
    weight: 5
    region: content
  field_plain_description:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: content
  field_start_time:
    type: simple_time_formatter_three
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  field_subtitle:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 2
    region: content
hidden:
  search_api_attachments: true
  search_api_excerpt: true
