uuid: 495d41c5-c3ed-4680-af3b-3f0f0ce15cb4
langcode: en
status: true
dependencies:
  config:
    - node.type.event
id: node.event.moderation_state
field_name: moderation_state
entity_type: node
bundle: event
label: 'Moderation state'
description: 'The moderation state of this piece of content.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
