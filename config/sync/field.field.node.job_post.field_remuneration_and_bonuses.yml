uuid: 9e21fc0d-0660-4b0d-8c3a-919adb2a1c06
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_remuneration_and_bonuses
    - node.type.job_post
    - taxonomy.vocabulary.remuneration_and_bonuses
id: node.job_post.field_remuneration_and_bonuses
field_name: field_remuneration_and_bonuses
entity_type: node
bundle: job_post
label: 'Заплащане и бонуси'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      remuneration_and_bonuses: remuneration_and_bonuses
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
