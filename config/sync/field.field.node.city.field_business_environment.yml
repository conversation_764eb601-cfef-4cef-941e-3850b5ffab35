uuid: e2bcc257-4f5e-4cc2-abcd-ba3b1243a180
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_business_environment
    - node.type.city
    - paragraphs.paragraphs_type.business_environment
    - paragraphs.paragraphs_type.employers
  module:
    - entity_reference_revisions
id: node.city.field_business_environment
field_name: field_business_environment
entity_type: node
bundle: city
label: 'Бизнес среда'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      business_environment: business_environment
      employers: employers
    negate: 0
    target_bundles_drag_drop:
      business_environment:
        weight: 7
        enabled: true
      company:
        weight: 8
        enabled: false
      employers:
        weight: 8
        enabled: true
      partner:
        weight: 9
        enabled: false
      schedule_day:
        weight: 10
        enabled: false
      schedule_slot:
        weight: 11
        enabled: false
      social_media_link:
        weight: 12
        enabled: false
field_type: entity_reference_revisions
