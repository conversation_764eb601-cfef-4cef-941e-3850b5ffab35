uuid: 95e994a7-4bd5-456e-969e-3fb2c4e5ac65
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_gallery
    - media.type.image
    - media.type.remote_video
    - node.type.news
id: node.news.field_gallery
field_name: field_gallery
entity_type: node
bundle: news
label: Gallery
description: Галерия
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
      remote_video: remote_video
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: image
field_type: entity_reference
