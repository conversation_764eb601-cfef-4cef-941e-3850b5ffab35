uuid: d0b7e99b-076f-4a26-8092-673ea74e2c12
langcode: en
status: true
dependencies:
  config:
    - node.type.speaker
  module:
    - metatag
id: node.speaker.metatag
field_name: metatag
entity_type: node
bundle: speaker
label: 'Metatags (Hidden field for JSON support)'
description: 'The computed meta tags for the entity.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: metatag_computed
