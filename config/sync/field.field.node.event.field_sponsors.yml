uuid: 75f6b636-1949-40d8-97af-7da8f59c47ee
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_sponsors
    - node.type.event
    - paragraphs.paragraphs_type.company
    - paragraphs.paragraphs_type.partner
  module:
    - entity_reference_revisions
id: node.event.field_sponsors
field_name: field_sponsors
entity_type: node
bundle: event
label: Sponsors
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      company: company
      partner: partner
    negate: 0
    target_bundles_drag_drop:
      company:
        weight: 8
        enabled: true
      partner:
        weight: 9
        enabled: true
      schedule_day:
        weight: 10
        enabled: false
      schedule_slot:
        weight: 3
        enabled: false
      social_media_link:
        weight: 4
        enabled: false
      supporter:
        weight: 7
        enabled: false
field_type: entity_reference_revisions
