uuid: 204b259a-c4b9-4db2-9090-14184b5acd08
langcode: en
status: true
dependencies:
  config:
    - field.field.node.job_post.field_additional_benefits
    - field.field.node.job_post.field_allocated_budget
    - field.field.node.job_post.field_annual_leave
    - field.field.node.job_post.field_annual_leave_days
    - field.field.node.job_post.field_description_requiremens
    - field.field.node.job_post.field_development_and_training
    - field.field.node.job_post.field_education
    - field.field.node.job_post.field_family_and_social_support
    - field.field.node.job_post.field_food_and_beverage
    - field.field.node.job_post.field_food_voucher_amount
    - field.field.node.job_post.field_gross_net
    - field.field.node.job_post.field_health_and_insurance
    - field.field.node.job_post.field_languages
    - field.field.node.job_post.field_other_benefit
    - field.field.node.job_post.field_place_of_work
    - field.field.node.job_post.field_professional_field
    - field.field.node.job_post.field_reference_number
    - field.field.node.job_post.field_relocation_and_transport
    - field.field.node.job_post.field_remuneration_and_bonuses
    - field.field.node.job_post.field_salary_from
    - field.field.node.job_post.field_salary_to
    - field.field.node.job_post.field_sport_and_wellness
    - field.field.node.job_post.field_work_type
    - field.field.node.job_post.field_working_environment
    - field.field.node.job_post.field_working_hours
    - node.type.job_post
    - workflows.workflow.moderate_job_posts
  module:
    - content_moderation
    - entitygroupfield
    - field_group
    - path
    - scheduler
    - scheduler_content_moderation_integration
    - text
third_party_settings:
  field_group:
    group_main_details:
      children:
        - entitygroupfield
        - field_reference_number
        - title
        - group_two_columns_1
      label: 'Основни детайли'
      region: content
      parent_name: ''
      weight: 0
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: true
        description: ''
        required_fields: true
    group_professional_orientation:
      children:
        - group_two_columns_3
        - field_languages
      label: 'Професионално ориентиране'
      region: content
      parent_name: ''
      weight: 13
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: true
        description: ''
        required_fields: true
    group_salary:
      children:
        - group_two_columns_2
      label: 'Месечна заплата'
      region: content
      parent_name: ''
      weight: 11
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: true
        description: ''
        required_fields: true
    group_benefits:
      children:
        - field_working_hours
        - group_two_columns
      label: Придобивки
      region: content
      parent_name: ''
      weight: 16
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: true
        description: ''
        required_fields: true
    group_two_columns:
      children:
        - field_annual_leave
        - field_annual_leave_days
        - field_remuneration_and_bonuses
        - field_health_and_insurance
        - group_wrapper_3
        - group_wrapper_1
        - field_sport_and_wellness
        - field_relocation_and_transport
        - field_family_and_social_support
        - field_working_environment
        - group_wrapper
      label: 'two columns'
      region: content
      parent_name: group_benefits
      weight: 22
      format_type: html_element
      format_settings:
        classes: 'grid grid-cols-2 gap-5'
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: false
    group_description:
      children:
        - field_description_requiremens
      label: 'Описание и изисквания'
      region: content
      parent_name: ''
      weight: 12
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: true
        description: ''
        required_fields: false
    group_two_columns_1:
      children:
        - field_work_type
        - field_place_of_work
      label: 'two columns'
      region: content
      parent_name: group_main_details
      weight: 4
      format_type: html_element
      format_settings:
        classes: 'grid grid-cols-2 gap-5'
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: false
    group_two_columns_2:
      children:
        - field_salary_from
        - field_salary_to
        - field_gross_net
      label: 'two columns 2'
      region: content
      parent_name: group_salary
      weight: 24
      format_type: html_element
      format_settings:
        classes: 'grid grid-cols-2 gap-5'
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: false
    group_two_columns_3:
      children:
        - field_education
        - field_professional_field
      label: 'two columns 3'
      region: content
      parent_name: group_professional_orientation
      weight: 20
      format_type: html_element
      format_settings:
        classes: 'grid grid-cols-2 gap-5'
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: false
    group_wrapper:
      children:
        - field_additional_benefits
        - field_other_benefit
      label: wrapper
      region: content
      parent_name: group_two_columns
      weight: 32
      format_type: html_element
      format_settings:
        classes: 'flex flex-col gap-5'
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: false
    group_wrapper_1:
      children:
        - field_food_and_beverage
        - field_food_voucher_amount
      label: 'wrapper 1'
      region: content
      parent_name: group_two_columns
      weight: 27
      format_type: html_element
      format_settings:
        classes: 'flex flex-col gap-5'
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
    group_wrapper_3:
      children:
        - field_development_and_training
        - field_allocated_budget
      label: 'wrapper 3'
      region: content
      parent_name: group_two_columns
      weight: 26
      format_type: html_element
      format_settings:
        classes: 'flex flex-col gap-5'
        show_empty_fields: false
        id: ''
        label_as_html: false
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
id: node.job_post.default
targetEntityType: node
bundle: job_post
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  entitygroupfield:
    type: entitygroupfield_autocomplete_widget
    weight: 1
    region: content
    settings:
      help_text: ''
      label: Company
      multiple: false
      required: true
    third_party_settings: {  }
  field_additional_benefits:
    type: options_select
    weight: 18
    region: content
    settings: {  }
    third_party_settings: {  }
  field_allocated_budget:
    type: number
    weight: 28
    region: content
    settings:
      placeholder: ''
    third_party_settings:
      conditional_fields:
        cdd6a03c-82dc-4dec-8a32-aaeb7b7b631b:
          entity_type: node
          bundle: job_post
          dependee: field_development_and_training
          settings:
            state: visible
            reset: false
            condition: value
            grouping: AND
            values_set: 1
            value: ''
            values: {  }
            value_form:
              -
                target_id: '84'
            effect: show
            effect_options: {  }
            selector: ''
  field_annual_leave:
    type: options_select
    weight: 22
    region: content
    settings: {  }
    third_party_settings: {  }
  field_annual_leave_days:
    type: number
    weight: 23
    region: content
    settings:
      placeholder: ''
    third_party_settings:
      conditional_fields: {  }
  field_description_requiremens:
    type: text_textarea_with_summary
    weight: 13
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  field_development_and_training:
    type: options_select
    weight: 27
    region: content
    settings: {  }
    third_party_settings: {  }
  field_education:
    type: options_select
    weight: 21
    region: content
    settings: {  }
    third_party_settings: {  }
  field_family_and_social_support:
    type: options_select
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  field_food_and_beverage:
    type: options_select
    weight: 29
    region: content
    settings: {  }
    third_party_settings: {  }
  field_food_voucher_amount:
    type: number
    weight: 30
    region: content
    settings:
      placeholder: ''
    third_party_settings:
      conditional_fields:
        1ece84c9-064e-4bf8-bcb7-718d433ba68c:
          entity_type: node
          bundle: job_post
          dependee: field_food_and_beverage
          settings:
            state: visible
            reset: false
            condition: value
            grouping: AND
            values_set: 1
            value: ''
            values: {  }
            value_form:
              -
                target_id: '94'
            effect: show
            effect_options: {  }
            selector: ''
  field_gross_net:
    type: options_select
    weight: 25
    region: content
    settings: {  }
    third_party_settings:
      conditional_fields:
        05ccb704-794c-4e6c-bcc0-b8753b7b9806:
          entity_type: node
          bundle: job_post
          dependee: field_salary_range
          settings:
            state: '!visible'
            reset: false
            condition: empty
            grouping: AND
            values_set: 1
            value: ''
            values: {  }
            value_form:
              -
                from: ''
                to: ''
            effect: show
            effect_options: {  }
            selector: ''
  field_health_and_insurance:
    type: options_select
    weight: 25
    region: content
    settings: {  }
    third_party_settings: {  }
  field_languages:
    type: options_select
    weight: 23
    region: content
    settings: {  }
    third_party_settings: {  }
  field_other_benefit:
    type: string_textfield
    weight: 19
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      conditional_fields:
        2770a657-7110-4b85-b8e0-5bfe9a4401d3:
          entity_type: node
          bundle: job_post
          dependee: field_additional_benefits
          settings:
            state: visible
            reset: false
            condition: value
            grouping: AND
            values_set: 1
            value: ''
            values: {  }
            value_form:
              -
                target_id: '67'
            effect: show
            effect_options: {  }
            selector: ''
  field_place_of_work:
    type: options_select
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_professional_field:
    type: options_select
    weight: 22
    region: content
    settings: {  }
    third_party_settings: {  }
  field_reference_number:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_relocation_and_transport:
    type: options_select
    weight: 29
    region: content
    settings: {  }
    third_party_settings: {  }
  field_remuneration_and_bonuses:
    type: options_select
    weight: 24
    region: content
    settings: {  }
    third_party_settings: {  }
  field_salary_from:
    type: number
    weight: 23
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_salary_to:
    type: number
    weight: 24
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_sport_and_wellness:
    type: options_select
    weight: 28
    region: content
    settings: {  }
    third_party_settings: {  }
  field_work_type:
    type: options_select
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_working_environment:
    type: options_select
    weight: 31
    region: content
    settings: {  }
    third_party_settings: {  }
  field_working_hours:
    type: options_select
    weight: 21
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  scheduler_settings:
    weight: 14
    region: content
    settings: {  }
    third_party_settings: {  }
  simple_sitemap:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 10
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: options_select
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 15
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 18
    region: content
    settings: {  }
    third_party_settings: {  }
  url_redirects:
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  publish_on: true
  publish_state: true
