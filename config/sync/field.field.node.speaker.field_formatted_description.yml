uuid: 2dfbc866-67de-4013-b7eb-c44dbd981afa
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_formatted_description
    - node.type.speaker
  module:
    - text
id: node.speaker.field_formatted_description
field_name: field_formatted_description
entity_type: node
bundle: speaker
label: Description
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats: {  }
field_type: text_long
