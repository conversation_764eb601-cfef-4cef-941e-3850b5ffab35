uuid: 3327313a-6433-4ba6-a7f6-dbc2475e479a
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_additional_benefits
    - node.type.job_post
    - taxonomy.vocabulary.additional_benefits
id: node.job_post.field_additional_benefits
field_name: field_additional_benefits
entity_type: node
bundle: job_post
label: 'Допълнителни придобивки'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      additional_benefits: additional_benefits
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
