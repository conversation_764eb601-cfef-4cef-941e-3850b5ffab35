uuid: 08e34634-aafb-451e-8e09-9915338fd545
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.featured
    - field.field.node.event.field_calendar_link
    - field.field.node.event.field_cover_image
    - field.field.node.event.field_date
    - field.field.node.event.field_date_show_month_only
    - field.field.node.event.field_employers
    - field.field.node.event.field_formatted_description
    - field.field.node.event.field_gallery
    - field.field.node.event.field_listing_title
    - field.field.node.event.field_location
    - field.field.node.event.field_mailchimp_tag
    - field.field.node.event.field_place
    - field.field.node.event.field_promo_video
    - field.field.node.event.field_registration_status
    - field.field.node.event.field_schedule
    - field.field.node.event.field_sponsors
    - field.field.node.event.field_teaser_image
    - field.field.node.event.field_video_gallery
    - node.type.event
  module:
    - address
    - entity_reference_revisions
    - link
    - text
    - user
id: node.event.featured
targetEntityType: node
bundle: event
mode: featured
content:
  field_calendar_link:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 112
    region: content
  field_cover_image:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 107
    region: content
  field_date_show_month_only:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 105
    region: content
  field_employers:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 114
    region: content
  field_formatted_description:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 102
    region: content
  field_gallery:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 109
    region: content
  field_listing_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 117
    region: content
  field_location:
    type: address_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 106
    region: content
  field_mailchimp_tag:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 113
    region: content
  field_place:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 116
    region: content
  field_promo_video:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 110
    region: content
  field_registration_status:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 102
    region: content
  field_schedule:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 101
    region: content
  field_sponsors:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 115
    region: content
  field_teaser_image:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 108
    region: content
  field_video_gallery:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 111
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  field_date: true
  langcode: true
  node_read_time: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
