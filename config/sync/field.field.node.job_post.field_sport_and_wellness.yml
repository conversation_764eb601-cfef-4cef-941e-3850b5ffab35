uuid: 2a656ce4-d4d7-492f-8666-ec6c01696088
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_sport_and_wellness
    - node.type.job_post
    - taxonomy.vocabulary.sport_and_wellness
id: node.job_post.field_sport_and_wellness
field_name: field_sport_and_wellness
entity_type: node
bundle: job_post
label: 'Спорт и уелнес'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      sport_and_wellness: sport_and_wellness
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
