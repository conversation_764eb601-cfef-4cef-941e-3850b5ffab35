uuid: 67e04d8a-d012-49db-ab18-09339ac9b8d0
langcode: bg
status: true
dependencies:
  config:
    - field.field.node.landing_page.field_listings
    - field.field.node.landing_page.field_metatags
    - node.type.landing_page
  module:
    - content_moderation
    - metatag
    - path
    - viewsreference
id: node.landing_page.default
targetEntityType: node
bundle: landing_page
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_listings:
    type: viewsreference_select
    weight: 121
    region: content
    settings: {  }
    third_party_settings: {  }
  field_metatags:
    type: metatag_firehose
    weight: 122
    region: content
    settings:
      sidebar: true
      use_details: true
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 100
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  simple_sitemap:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  entitygroupfield: true
  publish_on: true
  publish_state: true
  unpublish_on: true
  unpublish_state: true
