uuid: 19e664c9-af44-45a8-814d-132e06ffd1f1
langcode: en
status: true
dependencies:
  config:
    - field.field.node.event.field_calendar_link
    - field.field.node.event.field_cover_image
    - field.field.node.event.field_date
    - field.field.node.event.field_date_show_month_only
    - field.field.node.event.field_employers
    - field.field.node.event.field_formatted_description
    - field.field.node.event.field_gallery
    - field.field.node.event.field_listing_title
    - field.field.node.event.field_location
    - field.field.node.event.field_mailchimp_tag
    - field.field.node.event.field_place
    - field.field.node.event.field_promo_video
    - field.field.node.event.field_registration_status
    - field.field.node.event.field_schedule
    - field.field.node.event.field_sponsors
    - field.field.node.event.field_teaser_image
    - field.field.node.event.field_video_gallery
    - node.type.event
  module:
    - address
    - entity_reference_revisions
    - text
    - user
id: node.event.default
targetEntityType: node
bundle: event
mode: default
content:
  field_cover_image:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 4
    region: content
  field_employers:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 10
    region: content
  field_formatted_description:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  field_gallery:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 6
    region: content
  field_location:
    type: address_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_place:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_promo_video:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: listing
      link: false
    third_party_settings: {  }
    weight: 5
    region: content
  field_schedule:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 8
    region: content
  field_sponsors:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 9
    region: content
  field_video_gallery:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 7
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  field_calendar_link: true
  field_date: true
  field_date_show_month_only: true
  field_listing_title: true
  field_mailchimp_tag: true
  field_registration_status: true
  field_teaser_image: true
  langcode: true
  node_read_time: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
