uuid: 5e166bec-e23b-4e52-9fc1-22acd05990fd
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - node.type.event
  module:
    - datetime
    - node
    - ui_patterns
    - ui_patterns_views
    - user
  theme:
    - bwy
id: events
label: Events
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Upcoming events'
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 6
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        field_start_date_value:
          id: field_start_date_value
          table: node__field_start_date
          field: field_start_date_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        type:
          id: type
          table: node_field_data
          field: type
          entity_type: node
          entity_field: type
          plugin_id: bundle
          value:
            event: event
        field_date_value:
          id: field_date_value
          table: node__field_date
          field: field_date_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: ui_patterns
        options:
          uses_fields: false
          ui_patterns:
            ui_patterns:
              component_id: 'bwy:grid_cols'
              variant_id: null
              props:
                attributes:
                  source_id: attributes
                  source:
                    value: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6'
              slots:
                content:
                  sources:
                    -
                      source_id: view_rows
                      source:
                        ui_patterns_views_field: ''
                      _weight: '0'
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: teaser
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      css_class: ''
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  events_news:
    id: events_news
    display_title: 'Events in News'
    display_plugin: block
    position: 1
    display_options:
      title: 'Events in news'
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      defaults:
        title: false
        css_class: false
        pager: false
      css_class: ''
      display_description: ''
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  upcoming_events:
    id: upcoming_events
    display_title: 'Upcoming events'
    display_plugin: block
    position: 1
    display_options:
      display_description: ''
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
