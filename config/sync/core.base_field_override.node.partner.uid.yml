uuid: a534d8a2-a604-4683-9fa4-9c3fc3df4dca
langcode: bg
status: true
dependencies:
  config:
    - node.type.partner
id: node.partner.uid
field_name: uid
entity_type: node
bundle: partner
label: 'Authored by'
description: 'The username of the content author.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
