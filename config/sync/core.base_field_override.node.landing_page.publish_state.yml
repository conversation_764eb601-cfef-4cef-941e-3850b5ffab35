uuid: 0e6f33e1-28ff-45b5-b384-2a0a476a71b9
langcode: bg
status: true
dependencies:
  config:
    - node.type.landing_page
  module:
    - options
id: node.landing_page.publish_state
field_name: publish_state
entity_type: node
bundle: landing_page
label: 'Publish state'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
