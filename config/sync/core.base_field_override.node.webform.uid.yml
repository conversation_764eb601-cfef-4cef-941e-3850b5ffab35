uuid: 57fe2018-ad56-44e5-99af-6f5da46439a5
langcode: bg
status: true
dependencies:
  config:
    - node.type.webform
id: node.webform.uid
field_name: uid
entity_type: node
bundle: webform
label: 'Authored by'
description: 'The username of the content author.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
