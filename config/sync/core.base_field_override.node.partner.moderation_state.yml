uuid: 3f12ca98-c2da-4f85-a2fa-1aaf2e3c53d7
langcode: bg
status: true
dependencies:
  config:
    - node.type.partner
id: node.partner.moderation_state
field_name: moderation_state
entity_type: node
bundle: partner
label: 'Moderation state'
description: 'The moderation state of this piece of content.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
