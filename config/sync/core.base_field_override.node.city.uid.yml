uuid: b99eece6-dd4c-4562-8a2d-73455cba8609
langcode: bg
status: true
dependencies:
  config:
    - node.type.city
id: node.city.uid
field_name: uid
entity_type: node
bundle: city
label: 'Authored by'
description: 'The username of the content author.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
