uuid: bd027a8a-439e-4722-8887-67b35463a1c5
langcode: bg
status: true
dependencies:
  config:
    - field.field.node.landing_page.field_listings
    - field.field.node.landing_page.field_metatags
    - node.type.landing_page
  module:
    - metatag
    - user
    - viewsreference
id: node.landing_page.default
targetEntityType: node
bundle: landing_page
mode: default
content:
  field_listings:
    type: viewsreference_formatter
    label: hidden
    settings:
      plugin_types:
        page: page
        block: block
        default: '0'
        entity_reference: '0'
        feed: '0'
        attachment: '0'
        embed: '0'
    third_party_settings: {  }
    weight: 101
    region: content
  field_metatags:
    type: metatag_empty_formatter
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 102
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  langcode: true
  node_read_time: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
