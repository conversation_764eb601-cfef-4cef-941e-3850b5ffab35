uuid: 32f02e19-9183-4477-8faa-8ca8e2e34d7c
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_work_type
    - node.type.job_post
    - taxonomy.vocabulary.work_type
id: node.job_post.field_work_type
field_name: field_work_type
entity_type: node
bundle: job_post
label: 'Модел на работа'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      work_type: work_type
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
