uuid: dc914a39-240b-435b-87e0-b35735b7ede3
langcode: bg
status: true
dependencies:
  config:
    - node.type.city
id: node.city.moderation_state
field_name: moderation_state
entity_type: node
bundle: city
label: 'Moderation state'
description: 'The moderation state of this piece of content.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
