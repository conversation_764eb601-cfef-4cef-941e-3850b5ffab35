uuid: 08db56ff-fa02-4f77-b652-d1f8a5a1e1f3
langcode: bg
status: true
dependencies:
  config:
    - node.type.news
id: node.news.uid
field_name: uid
entity_type: node
bundle: news
label: 'Authored by'
description: 'The username of the content author.'
required: false
translatable: false
default_value: {  }
default_value_callback: 'Drupal\node\Entity\Node::getDefaultEntityOwner'
settings:
  handler: default
  handler_settings: {  }
field_type: entity_reference
