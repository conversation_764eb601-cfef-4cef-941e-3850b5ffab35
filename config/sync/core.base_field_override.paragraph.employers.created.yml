uuid: 129fe9f5-9fb1-4b08-a286-863cd7c7df16
langcode: bg
status: true
dependencies:
  config:
    - paragraphs.paragraphs_type.employers
id: paragraph.employers.created
field_name: created
entity_type: paragraph
bundle: employers
label: 'Authored on'
description: 'The time that the Paragraph was created.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: created
