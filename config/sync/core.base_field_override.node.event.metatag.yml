uuid: 973b520a-4fea-471c-a00c-65f6b9bd421d
langcode: en
status: true
dependencies:
  config:
    - node.type.event
  module:
    - metatag
id: node.event.metatag
field_name: metatag
entity_type: node
bundle: event
label: 'Metatags (Hidden field for JSON support)'
description: 'The computed meta tags for the entity.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: metatag_computed
