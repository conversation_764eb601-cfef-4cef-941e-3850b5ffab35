uuid: 1b6bd73f-f873-4d19-b56c-28828d08d14e
langcode: bg
status: true
dependencies:
  config:
    - node.type.job_post
  module:
    - metatag
id: node.job_post.metatag
field_name: metatag
entity_type: node
bundle: job_post
label: 'Metatags (Hidden field for JSON support)'
description: 'The computed meta tags for the entity.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: metatag_computed
