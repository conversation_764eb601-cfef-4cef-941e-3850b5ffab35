uuid: e559b703-ed9a-42c3-a5e4-d41cb5c34fd2
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_calculator_data
    - node.type.city
  module:
    - bwy_calculator
id: node.city.field_calculator_data
field_name: field_calculator_data
entity_type: node
bundle: city
label: 'Данни от калкулатора'
description: ''
required: true
translatable: true
default_value:
  -
    kids: {  }
    transportation: {  }
    entertainment: {  }
    eating_out: {  }
    housing: {  }
    fixed: {  }
    kindergarten: 0.0
    public_transport: 0.0
    fuel: 0.0
    biking_walking: 0.0
    going_out: 0.0
    restaurant: 0.0
    rent: 0.0
    own_house: 0.0
    mortgage: 0.0
    food: 0.0
    utilities: 0.0
default_value_callback: ''
settings: {  }
field_type: bwy_calculator_data
