uuid: 394d388e-29c3-4d3e-ac8d-62c3ccbe6f4f
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.profile.listing
    - field.field.profile.talent.field_certificates
    - field.field.profile.talent.field_cv
    - field.field.profile.talent.field_education
    - field.field.profile.talent.field_first_name
    - field.field.profile.talent.field_general_consent
    - field.field.profile.talent.field_interested_city
    - field.field.profile.talent.field_languages
    - field.field.profile.talent.field_last_name
    - field.field.profile.talent.field_linkedin
    - field.field.profile.talent.field_phone
    - field.field.profile.talent.field_photo
    - field.field.profile.talent.field_professional_field
    - field.field.profile.talent.field_residence_place
    - field.field.profile.talent.field_return_reason
    - field.field.profile.talent.field_subscribe_jobs
    - field.field.profile.talent.field_subscribe_marketing
    - field.field.profile.talent.field_talent_profile_consent
    - image.style.medium
    - profile.type.talent
  module:
    - file
    - image
    - link
id: profile.talent.listing
targetEntityType: profile
bundle: talent
mode: listing
content:
  field_cv:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: false
    third_party_settings: {  }
    weight: 5
    region: content
  field_first_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_last_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 2
    region: content
  field_linkedin:
    type: link
    label: above
    settings:
      trim_length: null
      url_only: false
      url_plain: false
      rel: nofollow
      target: _blank
    third_party_settings: {  }
    weight: 4
    region: content
  field_photo:
    type: image
    label: hidden
    settings:
      image_link: content
      image_style: medium
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
  field_residence_place:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  invite_link:
    settings: {  }
    third_party_settings: {  }
    weight: 6
    region: content
hidden:
  field_certificates: true
  field_education: true
  field_general_consent: true
  field_interested_city: true
  field_languages: true
  field_phone: true
  field_professional_field: true
  field_return_reason: true
  field_subscribe_jobs: true
  field_subscribe_marketing: true
  field_talent_profile_consent: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
